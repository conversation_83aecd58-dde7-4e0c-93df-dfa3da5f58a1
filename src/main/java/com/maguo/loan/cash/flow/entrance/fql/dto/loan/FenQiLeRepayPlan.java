package com.maguo.loan.cash.flow.entrance.fql.dto.loan;

import java.math.BigDecimal;

public class FenQiLeRepayPlan {
        /**
         * 资方放款编号
         */
        private String capitalLoanNo;
        /**
         * 当前期数
         */
        private Integer loanTerm;
        /**
         * 资方放款成功日期,YYYY-MM-DD HH:MM:SS
         */
        private String paymentTime;
        /**
         * 应还日,YYYY-MM-DD
         */
        private String expectRepayDate;
        /**
         * 当期还款状态
         */
        private Integer repayStatus;
        /**
         * 应还总额(元)
         */
        private BigDecimal expectRepayAmount = BigDecimal.ZERO;
        /**
         * 应还本金(元)
         */
        private BigDecimal expectRepayPrincipal = BigDecimal.ZERO;
        /**
         * 应还利息(元)
         */
        private BigDecimal expectRepayInterest = BigDecimal.ZERO;
        /**
         * 应还罚息(元)
         */
        private BigDecimal expectRepayPenaltyInterest = BigDecimal.ZERO;
        /**
         * 应还担保费(元)
         */
        private BigDecimal expectRepayGuarantee = BigDecimal.ZERO;
        /**
         * 应还信用评估费(元)
         */
        private BigDecimal expectRepayCreditFee = BigDecimal.ZERO;
        /**
         * 应还担保咨询服务费
         */
        private BigDecimal expectGranteeConsultServiceFee = BigDecimal.ZERO;
        /**
         * 扩展字段
         */
        private String extendFields;
        /**
         * 剩余本金(元)
         */
        private BigDecimal remainingPrincipal = BigDecimal.ZERO;

        public String getCapitalLoanNo() {
            return capitalLoanNo;
        }

        public void setCapitalLoanNo(String capitalLoanNo) {
            this.capitalLoanNo = capitalLoanNo;
        }

        public Integer getLoanTerm() {
            return loanTerm;
        }

        public void setLoanTerm(Integer loanTerm) {
            this.loanTerm = loanTerm;
        }

        public String getPaymentTime() {
            return paymentTime;
        }

        public void setPaymentTime(String paymentTime) {
            this.paymentTime = paymentTime;
        }

        public String getExpectRepayDate() {
            return expectRepayDate;
        }

        public void setExpectRepayDate(String expectRepayDate) {
            this.expectRepayDate = expectRepayDate;
        }

        public Integer getRepayStatus() {
            return repayStatus;
        }

        public void setRepayStatus(Integer repayStatus) {
            this.repayStatus = repayStatus;
        }

        public BigDecimal getExpectRepayAmount() {
            return expectRepayAmount;
        }

        public void setExpectRepayAmount(BigDecimal expectRepayAmount) {
            this.expectRepayAmount = expectRepayAmount;
        }

        public BigDecimal getExpectRepayPrincipal() {
            return expectRepayPrincipal;
        }

        public void setExpectRepayPrincipal(BigDecimal expectRepayPrincipal) {
            this.expectRepayPrincipal = expectRepayPrincipal;
        }

        public BigDecimal getExpectRepayInterest() {
            return expectRepayInterest;
        }

        public void setExpectRepayInterest(BigDecimal expectRepayInterest) {
            this.expectRepayInterest = expectRepayInterest;
        }

        public BigDecimal getExpectRepayPenaltyInterest() {
            return expectRepayPenaltyInterest;
        }

        public void setExpectRepayPenaltyInterest(BigDecimal expectRepayPenaltyInterest) {
            this.expectRepayPenaltyInterest = expectRepayPenaltyInterest;
        }

        public BigDecimal getExpectRepayGuarantee() {
            return expectRepayGuarantee;
        }

        public void setExpectRepayGuarantee(BigDecimal expectRepayGuarantee) {
            this.expectRepayGuarantee = expectRepayGuarantee;
        }

        public BigDecimal getExpectRepayCreditFee() {
            return expectRepayCreditFee;
        }

        public void setExpectRepayCreditFee(BigDecimal expectRepayCreditFee) {
            this.expectRepayCreditFee = expectRepayCreditFee;
        }

        public BigDecimal getExpectGranteeConsultServiceFee() {
            return expectGranteeConsultServiceFee;
        }

        public void setExpectGranteeConsultServiceFee(BigDecimal expectGranteeConsultServiceFee) {
            this.expectGranteeConsultServiceFee = expectGranteeConsultServiceFee;
        }

        public String getExtendFields() {
            return extendFields;
        }

        public void setExtendFields(String extendFields) {
            this.extendFields = extendFields;
        }

        public BigDecimal getRemainingPrincipal() {
            return remainingPrincipal;
        }

        public void setRemainingPrincipal(BigDecimal remainingPrincipal) {
            this.remainingPrincipal = remainingPrincipal;
        }
    }
