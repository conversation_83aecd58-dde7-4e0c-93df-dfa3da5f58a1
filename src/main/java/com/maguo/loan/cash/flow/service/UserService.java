package com.maguo.loan.cash.flow.service;


import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.crypt.DigestUtil;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.convert.UserInfoConvert;
import com.maguo.loan.cash.flow.entity.PreOrder;
import com.maguo.loan.cash.flow.entity.UserContactInfo;
import com.maguo.loan.cash.flow.entity.UserDevice;
import com.maguo.loan.cash.flow.entity.UserFace;
import com.maguo.loan.cash.flow.entity.UserInfo;
import com.maguo.loan.cash.flow.entity.UserInfoExpand;
import com.maguo.loan.cash.flow.entity.UserOcr;
import com.maguo.loan.cash.flow.entity.UserRegister;
import com.maguo.loan.cash.flow.entity.UserRiskRecord;
import com.maguo.loan.cash.flow.entity.ppd.PpdCreditApplyRecord;
import com.maguo.loan.cash.flow.entrance.lvxin.exception.LvxinBizException;
import com.maguo.loan.cash.flow.entrance.lvxin.exception.LvxinResultCode;
import com.maguo.loan.cash.flow.entrance.ppd.config.PpdConfig;
import com.maguo.loan.cash.flow.enums.ApplyChannel;
import com.maguo.loan.cash.flow.enums.ApplyType;
import com.maguo.loan.cash.flow.enums.AuditState;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.IsIncludingEquity;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.RateLevel;
import com.maguo.loan.cash.flow.repository.PreOrderRepository;
import com.maguo.loan.cash.flow.repository.UserAccountRepository;
import com.maguo.loan.cash.flow.repository.UserContactInfoRepository;
import com.maguo.loan.cash.flow.repository.UserDeviceRepository;
import com.maguo.loan.cash.flow.repository.UserFaceRepository;
import com.maguo.loan.cash.flow.repository.UserInfoExpandRepository;
import com.maguo.loan.cash.flow.repository.UserInfoRepository;
import com.maguo.loan.cash.flow.repository.UserOcrRepository;
import com.maguo.loan.cash.flow.repository.UserRegisterRepository;
import com.maguo.loan.cash.flow.repository.UserRiskRecordRepository;
import com.maguo.loan.cash.flow.repository.ppd.PpdCreditApplyRecordRepository;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户服务
 */
@Service
public class UserService {

    private static final Logger logger = LoggerFactory.getLogger(UserService.class);

    @Autowired
    private UserRegisterRepository userRegisterRepository;

    @Autowired
    private UserInfoRepository userInfoRepository;

    @Autowired
    private UserOcrRepository userOcrRepository;

    @Autowired
    private UserFaceRepository userFaceRepository;

    @Autowired
    private UserDeviceRepository userDeviceRepository;

    @Autowired
    private UserContactInfoRepository userContactInfoRepository;

    @Autowired
    private UserRiskRecordRepository riskRecordRepository;

    @Autowired
    private UserInfoExpandRepository userInfoExpandRepository;

    @Autowired
    private PpdConfig ppdConfig;


    @Autowired
    private UserAccountRepository userAccountRepository;

    @Autowired
    private PreOrderRepository preOrderRepository;

    @Autowired
    private PpdCreditApplyRecordRepository ppdCreditApplyRecordRepository;


    public UserInfo userExist(String phone) {
        return userInfoRepository.findByMobile(phone);
    }

    /**
     * 中原用户注册
     *
     * @param phone  手机号码
     * @param openId 中原用户id
     * @return
     */
    public UserRegister registerRecord(String phone, String openId, FlowChannel channel) {

        UserRegister userRegister = new UserRegister();
        userRegister.setMobile(phone);
        userRegister.setOpenId(openId);
        userRegister.setSourceChannel(channel);
        userRegister.setRegisterState(ProcessState.SUCCEED);

        return userRegisterRepository.save(userRegister);
    }

    public UserRegister findUserRegisterByOpenId(String openId, FlowChannel channel) {
        return userRegisterRepository.findTopByOpenIdAndSourceChannel(openId, channel);
    }

    public UserInfo findUserInfo(String userId) {
        return userInfoRepository.findById(userId).orElseThrow(() -> new BizException(ResultCode.USER_INFO_NOT_EXIST));
    }

    public UserOcr findUserOcr(String userId) {
        return userOcrRepository.findByUserId(userId);
    }

    public UserFace findUserFace(String userId) {
        return userFaceRepository.findByUserId(userId);
    }

    /**
     * 给之前绿信
     *
     * @param userInfo
     * @param ocr
     * @param face
     * @param device
     * @param contactInfos
     * @param flowChannel
     * @return
     */
    public UserRiskRecord register(UserInfo userInfo, UserOcr ocr, UserFace face, UserDevice device, List<UserContactInfo> contactInfos, FlowChannel flowChannel,PreOrder preOrder) {
        return this.register(userInfo, ocr, face, device, contactInfos, flowChannel, ApplyChannel.LVXIN.getCode(),preOrder);
    }

    /**
     * 用户注册保存用户信息
     *
     * @param userInfo     用户信息
     * @param ocr          身份证ocr
     * @param face         人脸识别
     * @param device       设备
     * @param contactInfos 联系人信息
     * @return 用户风控记录
     */
    public UserRiskRecord register(UserInfo userInfo, UserOcr ocr, UserFace face, UserDevice device, List<UserContactInfo> contactInfos, FlowChannel flowChannel, String applyChannel,PreOrder preOrder) {

        UserInfo existUserInfo = userInfoRepository.findByCertNo(userInfo.getCertNo());
        if (existUserInfo == null) {
            if (userInfo.getId() != null) {
                UserInfo user = userInfoRepository.findById(userInfo.getId()).orElse(null);
                if (!ObjectUtils.isEmpty(user)) {
                    throw new LvxinBizException(LvxinResultCode.USER_ID_AREADY_EXISTS);
                }
            }

            existUserInfo = createUser(userInfo, ocr, face, device, contactInfos);
            // 扩展信息
            saveUserInfoExpand(existUserInfo);
        } else {
            existUserInfo = updateUser(existUserInfo, userInfo, ocr, face, device, contactInfos);
        }
        return createRiskRecord(existUserInfo.getId(), flowChannel, applyChannel, userInfo.getCertNo(),preOrder.getOrderNo());
    }


    /**
     * registerLoan
     *
     * @param userInfo
     * @param ocr
     * @param face
     * @param device
     * @param contactInfos
     * @param flowChannel
     * @param applyChannel
     * @return
     */
    public UserRiskRecord registerLoan(UserInfo userInfo, UserOcr ocr, UserFace face, UserDevice device, List<UserContactInfo> contactInfos, FlowChannel flowChannel, String applyChannel) {

        UserInfo existUserInfo = userInfoRepository.findByCertNo(userInfo.getCertNo());
        if (existUserInfo == null) {
            if (userInfo.getId() != null) {
                UserInfo user = userInfoRepository.findById(userInfo.getId()).orElse(null);
                if (!ObjectUtils.isEmpty(user)) {
                    throw new LvxinBizException(LvxinResultCode.USER_ID_AREADY_EXISTS);
                }
            }

            existUserInfo = createUser(userInfo, ocr, face, device, contactInfos);
            // 扩展信息
            saveUserInfoExpand(existUserInfo);
        } else {
            existUserInfo = updateUser(existUserInfo, userInfo, ocr, face, device, contactInfos);
        }
        return createRiskLoanRecord(existUserInfo.getId(), flowChannel, applyChannel);
    }

    private UserInfo createUser(final UserInfo userInfo, final UserOcr ocr, final UserFace face, final UserDevice device,
                                final List<UserContactInfo> contactInfos) {
        UserInfo user = userInfoRepository.save(userInfo);
        final String userId = user.getId();

        ocr.setUserId(userId);
        userOcrRepository.save(ocr);

        face.setUserId(userId);
        userFaceRepository.save(face);

        device.setUserId(userId);
        userDeviceRepository.save(device);

        contactInfos.forEach(c -> c.setUserId(userId));
        userContactInfoRepository.saveAll(contactInfos);

        return user;
    }

    private UserInfo updateUser(final UserInfo existUserInfo, final UserInfo userInfo, final UserOcr ocr, final UserFace face, final UserDevice device,
                                final List<UserContactInfo> contactInfos) {
        final String userId = existUserInfo.getId();

        UserInfoConvert.INSTANCE.updateUserInfo(existUserInfo, userInfo);

        userInfoRepository.save(existUserInfo);

        UserOcr existOcr = userOcrRepository.findByUserId(userId);
        if (existOcr != null) {
            userOcrRepository.delete(existOcr);
        }
        ocr.setUserId(userId);
        userOcrRepository.save(ocr);


        UserFace existFace = userFaceRepository.findByUserId(userId);
        if (existFace != null) {
            userFaceRepository.delete(existFace);
        }
        face.setUserId(userId);
        userFaceRepository.save(face);


        UserDevice existDevice = userDeviceRepository.findByUserId(userId);
        if (existDevice != null) {
            userDeviceRepository.delete(existDevice);
        }
        device.setUserId(userId);
        userDeviceRepository.save(device);

        userContactInfoRepository.deleteByUserId(userId);
        contactInfos.forEach(c -> c.setUserId(userId));
        userContactInfoRepository.saveAll(contactInfos);

        return existUserInfo;
    }

    public UserRiskRecord createRiskRecord(String userId, FlowChannel flowChannel, String applyChannel, String certNo,String orderNo) {
        UserRiskRecord record = new UserRiskRecord();
        record.setUserId(userId);
        record.setApproveResult(AuditState.INIT);
        record.setFlowChannel(flowChannel);
        record.setApplyType(ApplyType.RISK);
        record.setApproveRate(RateLevel.RATE_36);
        // applyChannel == null 兼容绿信
        record.setApplyChannel(applyChannel);
        if (certNo.equals(ppdConfig.getWhiteList()) && ppdConfig.getWhiteSwitch()) {
            record.setApproveRate(RateLevel.RATE_24);
        }
        if(FlowChannel.LVXIN.equals(flowChannel)) {
            //判断是不是权益客户
            PreOrder preOrder = preOrderRepository.findByOrderNoAndFlowChannel(orderNo, flowChannel).orElse(null);
            if (!ObjectUtils.isEmpty(preOrder) && IsIncludingEquity.Y.equals(preOrder.getIsIncludingEquity())) {
                record.setApproveRate(RateLevel.RATE_24);
            }
        }
        if (FlowChannel.FQLQY001.equals(flowChannel)){
            //分期乐利率24，默认权益
            record.setApproveRate(RateLevel.RATE_24);
        }
        return riskRecordRepository.save(record);
    }

    /**
     * createRiskLoanRecord
     *
     * @param userId
     * @param flowChannel
     * @param applyChannel
     * @return
     */
    public UserRiskRecord createRiskLoanRecord(String userId, FlowChannel flowChannel, String applyChannel) {
        UserRiskRecord record = new UserRiskRecord();
        record.setUserId(userId);
        record.setApproveResult(AuditState.INIT);
        record.setFlowChannel(flowChannel);
        record.setApplyType(ApplyType.RISK_LOAN);
        record.setApproveRate(RateLevel.RATE_36);
        // applyChannel == null 兼容绿信
        record.setApplyChannel(applyChannel);
        return riskRecordRepository.save(record);
    }


    /**
     * 用户注册记录填充userId
     *
     * @param openId
     * @param userId
     */
    public void fillRegisterRecordUserId(String openId, String userId, FlowChannel channel) {
        List<UserRegister> records = userRegisterRepository.findAllByOpenIdAndSourceChannel(openId, channel);
        for (UserRegister record : records) {
            if (record.getUserId() == null) {
                record.setUserId(userId);
                userRegisterRepository.save(record);
            }
        }
    }

    public void saveUserInfoExpand(UserInfo user) {
        try {
            String md5Mobile = DigestUtil.md5(user.getMobile());
            String certNo = DigestUtil.md5(user.getCertNo());
            UserInfoExpand userInfoExpand = userInfoExpandRepository.findById(user.getId()).orElseGet(UserInfoExpand::new);
            userInfoExpand.setId(user.getId());
            userInfoExpand.setMobile(md5Mobile);
            userInfoExpand.setCertNo(certNo);
            userInfoExpandRepository.save(userInfoExpand);
        } catch (Exception e) {
            logger.error("探知流量同步用户出错, userInfo: {}", JsonUtil.toJsonString(user));
        }
    }

    /**
     * 是否新老用户 true 老用户 false新用户
     *
     * @param certNo 身份证号码
     */

    public boolean isNewUser(String certNo, FlowChannel flowChannel) {
        return preOrderRepository.existsByCertNoAndFlowChannel(certNo, flowChannel);
    }

}
