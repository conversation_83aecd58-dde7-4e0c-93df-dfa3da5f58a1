package com.maguo.loan.cash.flow.entrance.fql.dto.repay;

import jakarta.validation.constraints.NotBlank;

import java.math.BigDecimal;

/**
 * @ClassName RealReturnNoticeRequest
 * <AUTHOR>
 * @Description 实还通知请求参数
 * @Date 2025/8/11 14:37
 * @Version v1.0
 **/
public class FenQiLeRealReturnNoticeRequest {

    /**
     * 合作方代码（资方定义，给乐信分配的代码）
     */
    @NotBlank(message = "合作方代码不能为空")
    private String partnerCode;
    /**
     * 还款请求流水号/账单号
     */
    @NotBlank(message = "还款请求流水号不能为空")
    private String billId;
    /**
     * 贷款申请编号
     */
    @NotBlank(message = "贷款申请编号不能为空")
    private String applyId;
    /**
     * 资金方放款编号/借据号
     */
    @NotBlank(message = "资金方放款编号/借据号不能为空")
    private String capitalLoanNo;
    /**
     * 还款期数
     */
    @NotBlank(message = "还款期数不能为空")
    private Integer repayTerm;
    /**
     * 还款类型
     * 不同还款类型分开通知,
     * 10-正常还款,
     * 30-提前结清,
     * 40-逾期还款
     * 50-代偿
     * 有的资金方不支持一天还款多种类型
     */
    @NotBlank(message = "还款类型不能为空")
    private Integer repayType;
    /**
     * 还款日期
     */
    @NotBlank(message = "还款日期不能为空")
    private String repayDate;
    /**
     * 用户实际还款日
     * 用户实际还款的日期，yyyymmdd
     */
    private String userRepayDate;
    /**
     * 还款路径
     * 1-清分 2-代扣
     */
    @NotBlank(message = "还款路径不能为空")
    private String repayChannel;
    /**
     * 还款总额,保留两位有效数字(单位:元)
     */
    @NotBlank(message = "还款总额不能为空")
    private BigDecimal repayAmount;
    /**
     * 实还本金,保留两位有效数字(单位:元)
     */
    @NotBlank(message = "实还本金不能为空")
    private BigDecimal repayPrincipal;
    /**
     * 实还利息,保留两位有效数字(单位:元)
     */
    @NotBlank(message = "实还利息不能为空")
    private BigDecimal repayInterest;
    /**
     * 实还罚息,保留两位有效数字(单位:元)
     */
    @NotBlank(message = "实还罚息不能为空")
    private BigDecimal repayMuclt;
    /**
     * 实还担保费,保留两位有效数字(单位:元)
     */
    private BigDecimal repayGuarantee;
    /**
     * 实还信用评估费,保留两位有效数字(单位:元)
     */
    private BigDecimal repayCreditFee;
    /**
     * 实还咨询服务费,保留两位有效数字(单位:元)
     */
    private BigDecimal repayGranteeConsultServiceFee;

    public String getPartnerCode() {
        return partnerCode;
    }

    public void setPartnerCode(String partnerCode) {
        this.partnerCode = partnerCode;
    }

    public String getBillId() {
        return billId;
    }

    public void setBillId(String billId) {
        this.billId = billId;
    }

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public String getCapitalLoanNo() {
        return capitalLoanNo;
    }

    public void setCapitalLoanNo(String capitalLoanNo) {
        this.capitalLoanNo = capitalLoanNo;
    }

    public Integer getRepayTerm() {
        return repayTerm;
    }

    public void setRepayTerm(Integer repayTerm) {
        this.repayTerm = repayTerm;
    }

    public Integer getRepayType() {
        return repayType;
    }

    public void setRepayType(Integer repayType) {
        this.repayType = repayType;
    }

    public String getRepayDate() {
        return repayDate;
    }

    public void setRepayDate(String repayDate) {
        this.repayDate = repayDate;
    }

    public String getUserRepayDate() {
        return userRepayDate;
    }

    public void setUserRepayDate(String userRepayDate) {
        this.userRepayDate = userRepayDate;
    }

    public String getRepayChannel() {
        return repayChannel;
    }

    public void setRepayChannel(String repayChannel) {
        this.repayChannel = repayChannel;
    }

    public BigDecimal getRepayAmount() {
        return repayAmount;
    }

    public void setRepayAmount(BigDecimal repayAmount) {
        this.repayAmount = repayAmount;
    }

    public BigDecimal getRepayPrincipal() {
        return repayPrincipal;
    }

    public void setRepayPrincipal(BigDecimal repayPrincipal) {
        this.repayPrincipal = repayPrincipal;
    }

    public BigDecimal getRepayInterest() {
        return repayInterest;
    }

    public void setRepayInterest(BigDecimal repayInterest) {
        this.repayInterest = repayInterest;
    }

    public BigDecimal getRepayMuclt() {
        return repayMuclt;
    }

    public void setRepayMuclt(BigDecimal repayMuclt) {
        this.repayMuclt = repayMuclt;
    }

    public BigDecimal getRepayGuarantee() {
        return repayGuarantee;
    }

    public void setRepayGuarantee(BigDecimal repayGuarantee) {
        this.repayGuarantee = repayGuarantee;
    }

    public BigDecimal getRepayCreditFee() {
        return repayCreditFee;
    }

    public void setRepayCreditFee(BigDecimal repayCreditFee) {
        this.repayCreditFee = repayCreditFee;
    }

    public BigDecimal getRepayGranteeConsultServiceFee() {
        return repayGranteeConsultServiceFee;
    }

    public void setRepayGranteeConsultServiceFee(BigDecimal repayGranteeConsultServiceFee) {
        this.repayGranteeConsultServiceFee = repayGranteeConsultServiceFee;
    }
}
