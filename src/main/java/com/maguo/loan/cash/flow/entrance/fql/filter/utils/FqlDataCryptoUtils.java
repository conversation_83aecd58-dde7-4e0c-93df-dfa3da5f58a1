package com.maguo.loan.cash.flow.entrance.fql.filter.utils;

import com.maguo.loan.cash.flow.entrance.fql.dto.FqlEncryptData;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.LvxinEncryptData;
import com.maguo.loan.cash.flow.entrance.lvxin.filter.utils.AESUtils;
import com.maguo.loan.cash.flow.entrance.lvxin.filter.utils.SHAUtils;
import com.maguo.loan.cash.flow.util.NumConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.security.Key;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.interfaces.RSAKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static java.nio.charset.StandardCharsets.UTF_8;

public class FqlDataCryptoUtils {

    private static Logger logger = LoggerFactory.getLogger(FqlDataCryptoUtils.class);

    /**
     * 验签解密
     *
     * @param requestData
     * @param rsaPublicKey
     * @param rsaPrivateKey
     * @return
     */
    public static boolean checkSignAndDecrypt(FqlEncryptData requestData, String rsaPublicKey, String rsaPrivateKey) {
        /**
         * 1.校验签名
         */
        String expected = sign(requestData);
        String sign;
        try {
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(Base64.getDecoder().decode(rsaPublicKey));
            sign = rsa(Cipher.DECRYPT_MODE, keyFactory.generatePublic(x509KeySpec), requestData.getSign());
        } catch (NoSuchAlgorithmException | InvalidKeySpecException e) {
            logger.error("sign解密失败", e);
            throw new RuntimeException("解密失败");
        }
        if (!expected.equals(sign)) {
            return false;
        }
        /**
         * 2.base64转码,用rsa算法对aes算法的秘钥进行解密
         */
        String aesKey;
        try {
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(Base64.getDecoder().decode(rsaPrivateKey));
            aesKey = rsa(Cipher.DECRYPT_MODE, keyFactory.generatePrivate(pkcs8KeySpec), requestData.getKey());
            requestData.setKey(aesKey);
        } catch (NoSuchAlgorithmException | InvalidKeySpecException e) {
            logger.error("AesKey解密失败", e);
            throw new RuntimeException("解密失败");
        }
        /**
         * 3.根据aeskey用aes算法对payload内容进行解密
         */
        if (!Objects.isNull(requestData.getBizContent())) {
            String payload = requestData.getBizContent();
            payload = AESUtils.decrypt(payload, aesKey);
            requestData.setBizContent(payload);
        }
        return true;
    }

    /**
     * 加密加签
     *
     * @param responseData
     * @param randomSeed    aesKey生成时的随机种子
     * @param rsaPublicKey
     * @param rsaPrivateKey
     */
    public static FqlEncryptData signAndEncrypt(FqlEncryptData responseData, String randomSeed, String rsaPublicKey, String rsaPrivateKey) {
        /**
         * 1.用rsa算法对aes算法的秘钥进行加密，并进行base64转码
         */
        String key;
        try {
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(Base64.getDecoder().decode(rsaPublicKey));
            key = rsa(Cipher.ENCRYPT_MODE, keyFactory.generatePublic(x509KeySpec), randomSeed);
        } catch (NoSuchAlgorithmException | InvalidKeySpecException e) {
            logger.error("Aeskey加密失败", e);
            throw new RuntimeException("加密失败");
        }
        responseData.setKey(key);
        /**
         * 2.用aes算法对payload内容进行加密，并进行base64转码
         */
        if (responseData.getBizContent() != null) {
            String payload = responseData.getBizContent();
            payload = AESUtils.encrypt(payload, randomSeed);
            responseData.setBizContent(payload);
        }
        /**
         * 3.加签
         */
        String sign = sign(responseData);
        try {
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(Base64.getDecoder().decode(rsaPrivateKey));
            sign = rsa(Cipher.ENCRYPT_MODE, keyFactory.generatePrivate(pkcs8KeySpec), sign);
        } catch (NoSuchAlgorithmException | InvalidKeySpecException e) {
            logger.error("sign加密失败", e);
            throw new RuntimeException("加密失败");
        }
        responseData.setSign(sign);
        return responseData;
    }

    private static String sign(FqlEncryptData data) {
        // 3.1 对channelCode、key、payload按key升顺排序，并以key=value&key=value形式拼接在一起
        Map signMap = new HashMap<String, Object>(NumConstants.THREE);
        signMap.put("key", data.getKey());
        signMap.put("bizcontent", data.getBizContent());
        signMap.put("partnerCode", data.getPartnerCode());
        String sign = formatUrlMap(signMap);
        // 3.2 对对拼接出来的字符串做SHA256计算，生成sign
        sign = SHAUtils.sha256(sign);
        return sign;
    }

    /**
     * 1. 对传入参数按key升顺排序
     * 2. 按顺序以key=value&key=value形式拼接在一起
     *
     * @param paraMap
     * @return
     */
    private static String formatUrlMap(Map<String, Object> paraMap) {
        String buff;
        Map<String, Object> tmpMap = paraMap;
        List<Map.Entry<String, Object>> infoIds = new ArrayList<>(tmpMap.entrySet());
        // 对所有传入参数按照字段名的 ASCII 码从小到大排序（字典序）
        Collections.sort(infoIds, Comparator.comparing(Map.Entry::getKey));
        // 构造URL 键值对的格式
        StringBuilder buf = new StringBuilder();
        for (Map.Entry<String, Object> item : infoIds) {
            if (Objects.nonNull(item.getKey())) {
                String key = item.getKey();
                Object val = item.getValue();
                buf.append(key + "=" + val);
                buf.append("&");
            }
        }
        buff = buf.toString();
        if (!buff.isEmpty()) {
            buff = buff.substring(0, buff.length() - 1);
        }
        return buff;
    }

    /**
     * rsa解密
     *
     * @param mode   加解密的模式
     * @param rsaKey key
     * @param data   数据
     * @return 加密后的字符串
     */
    private static String rsa(int mode, Key rsaKey, String data) {
        try {
            Cipher cipher = Cipher.getInstance("RSA");
            cipher.init(mode, rsaKey);
            byte[] originalBytes = mode == Cipher.ENCRYPT_MODE ? data.getBytes(UTF_8) : Base64.getDecoder().decode(data);
            byte[] bytesAfterDecrypt = rsaSplitCodec(cipher, mode, originalBytes, ((RSAKey) rsaKey).getModulus().bitLength());
            return mode == Cipher.ENCRYPT_MODE ? Base64.getEncoder().encodeToString(bytesAfterDecrypt)
                : new String(bytesAfterDecrypt, UTF_8);

        } catch (Exception e) {
            logger.error("RSA解密失败", e);
            throw new RuntimeException("解密失败");
        }
    }

    private static byte[] rsaSplitCodec(Cipher cipher, int opmode, byte[] datas, int keySize) {
        int maxBlock = opmode == Cipher.DECRYPT_MODE ? keySize / NumConstants.EIGHT : keySize / NumConstants.EIGHT - NumConstants.ELEVEN;
        int offSet = 0;
        byte[] buff;
        int i = 0;
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            while (datas.length > offSet) {
                if (datas.length - offSet > maxBlock) {
                    buff = cipher.doFinal(datas, offSet, maxBlock);
                } else {
                    buff = cipher.doFinal(datas, offSet, datas.length - offSet);
                }
                out.write(buff, 0, buff.length);
                i++;
                offSet = i * maxBlock;
            }
            return out.toByteArray();
        } catch (IOException | IllegalBlockSizeException | BadPaddingException e) {
            logger.error("RSA解密失败", e);
            throw new RuntimeException("解密失败");
        }
    }
}
