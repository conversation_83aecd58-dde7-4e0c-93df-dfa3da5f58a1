package com.maguo.loan.cash.flow.entrance.fql.dto.callBack;

import jakarta.validation.constraints.NotBlank;

/**
 * @Description 支用风控审核结果通知 请求参数
 * @Date 2025/08/15 16:51
 * @Version v1.0
 **/
public class BaiWeiDisburseRiskResultNoticeRequest {

    /**
     * 用信审批申请编号
     */
    @NotBlank(message = "用信审批申请编号不能为空")
    private String applyId;
    /**
     * 合作方代码
     */
    @NotBlank(message = "合作方代码不能为空")
    private String partnerCode;
    /**
     * 放款结果
     */
    @NotBlank(message = "放款结果不能为空")
    private Integer loanStatus;
    /**
     * 放款结果描述
     */
    @NotBlank(message = "放款结果描述不能为空")
    private String msg;

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public String getPartnerCode() {
        return partnerCode;
    }

    public void setPartnerCode(String partnerCode) {
        this.partnerCode = partnerCode;
    }

    public Integer getLoanStatus() {
        return loanStatus;
    }

    public void setLoanStatus(Integer loanStatus) {
        this.loanStatus = loanStatus;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
