package com.maguo.loan.cash.flow.entrance.fql.controller;

import com.maguo.loan.cash.flow.entrance.fql.dto.loan.LoanApplyRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.loan.LoanApplyResponse;
import com.maguo.loan.cash.flow.entrance.fql.dto.loan.LoanQueryRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.loan.LoanQueryResponse;
import com.maguo.loan.cash.flow.entrance.fql.dto.loan.RepayPlanQueryRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.loan.RepayPlanQueryResponse;
import com.maguo.loan.cash.flow.entrance.fql.service.FenQiLeService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/*
 * @Description: 分期乐放款接口
 * @Author: abai
 * @Date: 2025-8-11 下午 02:35
 */
@RestController
//@RequestMapping("/abai/api")
@RequestMapping("/fenQiLe/api")
public class FenQiLeLoanController extends FqlApiValidator{
    private static final Logger logger = LoggerFactory.getLogger(FenQiLeLoanController.class);

    @Autowired
    private FenQiLeService fenQiLeService;

    /**
     * 放款申请
     *
     * @param request
     * @return
     */
    @PostMapping("/fundApply")
    public LoanApplyResponse loanApply(@RequestBody @Valid LoanApplyRequest request, BindingResult bindingResult) {
        try {
            // 必填参数校验
            validate(bindingResult);

            // 业务逻辑
            return fenQiLeService.loanApply(request);
        }catch (Exception e){
            logger.error("分期乐放款失败", e);
            return LoanApplyResponse.fail("系统异常,请联系管理员");
        }
    }



    @RequestMapping("/fundQuery")
    public LoanQueryResponse loanQuery(@RequestBody @Valid LoanQueryRequest request, BindingResult bindingResult) {
        try {
            // 必填参数校验
            validate(bindingResult);

            return fenQiLeService.loanQuery(request);
        }catch (Exception e){
            logger.error("分期乐查询放款失败", e);
            return LoanQueryResponse.fail("系统异常,请联系管理员");
        }
    }

    @RequestMapping("/repayPlanSync")
    public RepayPlanQueryResponse repayPlanQuery(@RequestBody @Valid RepayPlanQueryRequest request, BindingResult bindingResult) {
        try {
            // 必填参数校验
            validate(bindingResult);

            // 业务逻辑
            return fenQiLeService.repayPlanQuery(request);
        }catch (Exception e){
            logger.error("分期乐查询还款计划失败", e);
            RepayPlanQueryResponse response = new RepayPlanQueryResponse();
            response.setStatus(1);
            response.setMsg("系统异常,请联系管理员");
            return response;
        }
    }
}
