package com.maguo.loan.cash.flow.entity;


import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.GuaranteeCompany;
import com.jinghang.common.util.StringUtil;
import com.maguo.loan.cash.flow.enums.AbleStatus;
import com.maguo.loan.cash.flow.enums.ProtocolChannel;
import com.maguo.loan.cash.flow.enums.WhetherState;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 资方配置
 *
 * <AUTHOR>
 * @TableName capital_config
 */
@Entity
@Table(name = "capital_config")
public class CapitalConfig extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 4583209707111367478L;
    /**
     * 资金方
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "bank_channel")
    private BankChannel bankChannel;

    /**
     * 融担公司
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "guarantee_company")
    private GuaranteeCompany guaranteeCompany;

    /**
     * 资方授信日限额
     */
    @Column(name = "credit_day_limit")
    private BigDecimal creditDayLimit;

    /**
     * 资方放款日限额
     */
    @Column(name = "loan_day_limit")
    private BigDecimal loanDayLimit;

    /**
     * 支持期数（逗号分割）
     */
    @Column(name = "periods_range")
    private String periodsRange;

    /**
     * 年龄区间
     */
    @Column(name = "ages_range")
    private String agesRange;

    /**
     * 单笔上下限
     */
    @Column(name = "single_amt_range")
    private String singleAmtRange;
    /**
     * 是否开启授信时间范围
     */
    @Enumerated(value = EnumType.STRING)
    @Column(name = "credit_time_status")
    private AbleStatus creditTimeStatus;
    /**
     * 是否开启放款时间范围限制
     */
    @Enumerated(value = EnumType.STRING)
    @Column(name = "loan_time_status")
    private AbleStatus loanTimeStatus;
    /**
     * 是否开启还款时间范围限制
     */
    @Enumerated(value = EnumType.STRING)
    @Column(name = "repay_time_status")
    private AbleStatus repayTimeStatus;
    /**
     * 授信开始HHmmss
     */
    @Column(name = "credit_start_time")
    private String creditStartTime;

    /**
     * 授信截止HHmmss
     */
    @Column(name = "credit_end_time")
    private String creditEndTime;

    /**
     * 放款开始HHmmss
     */
    @Column(name = "loan_start_time")
    private String loanStartTime;

    /**
     * 放款截止HHmmss
     */
    @Column(name = "loan_end_time")
    private String loanEndTime;

    /**
     * 启用状态
     */
    @Enumerated(EnumType.STRING)
    private AbleStatus enabled;

    /**
     * 资方利率
     */
    @Column(name = "bank_rate")
    private BigDecimal bankRate;

    /**
     * 资方支持利率
     */

    private String supportIrrLevel;

    /**
     * 还款开始时间
     */
    @Column(name = "repay_start_time")
    private String repayStartTime;

    /**
     * 还款结束时间
     */
    @Column(name = "repay_end_time")
    private String repayEndTime;

    /**
     * 是否可续借
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "renewed_flag")
    private WhetherState renewedFlag;

    /**
     * 绑卡渠道
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "protocol_channel")
    private ProtocolChannel protocolChannel;

    /**
     * 资金方简称
     */
    @Column(name = "capital_name_short")
    private String capitalNameShort;

    /**
     * 资金方主体全称
     */
    @Column(name = "capital_name")
    private String capitalName;

    /**
     * 资金方简介
     */
    @Column(name = "capital_introduction")
    private String capitalIntroduction;

    /**
     * 联系人
     */
    @Column(name = "contact_person")
    private String contactPerson;

    /**
     * 联系电话
     */
    @Column(name = "contact_phone")
    private String contactPhone;

    /**
     * 邮箱地址
     */
    @Column(name = "email_address")
    private String emailAddress;




    public BigDecimal getCreditDayLimit() {
        return creditDayLimit;
    }

    public void setCreditDayLimit(BigDecimal creditDayLimit) {
        this.creditDayLimit = creditDayLimit;
    }

    public BigDecimal getLoanDayLimit() {
        return loanDayLimit;
    }

    public void setLoanDayLimit(BigDecimal loanDayLimit) {
        this.loanDayLimit = loanDayLimit;
    }

    public String getPeriodsRange() {
        return periodsRange;
    }

    public void setPeriodsRange(String periodsRange) {
        this.periodsRange = periodsRange;
    }

    public String getAgesRange() {
        return agesRange;
    }

    public void setAgesRange(String agesRange) {
        this.agesRange = agesRange;
    }

    public String getSingleAmtRange() {
        return singleAmtRange;
    }

    public void setSingleAmtRange(String singleAmtRange) {
        this.singleAmtRange = singleAmtRange;
    }

    public String getCreditStartTime() {
        return creditStartTime;
    }

    public void setCreditStartTime(String creditStartTime) {
        this.creditStartTime = creditStartTime;
    }

    public String getCreditEndTime() {
        return creditEndTime;
    }

    public void setCreditEndTime(String creditEndTime) {
        this.creditEndTime = creditEndTime;
    }

    public String getLoanStartTime() {
        return loanStartTime;
    }

    public void setLoanStartTime(String loanStartTime) {
        this.loanStartTime = loanStartTime;
    }

    public String getLoanEndTime() {
        return loanEndTime;
    }

    public void setLoanEndTime(String loanEndTime) {
        this.loanEndTime = loanEndTime;
    }

    public AbleStatus getEnabled() {
        return enabled;
    }

    public void setEnabled(AbleStatus enabled) {
        this.enabled = enabled;
    }

    public BigDecimal getBankRate() {
        return bankRate;
    }

    public void setBankRate(BigDecimal bankRate) {
        this.bankRate = bankRate;
    }

    public String getSupportIrrLevel() {
        return supportIrrLevel;
    }

    public void setSupportIrrLevel(String supportIrrLevel) {
        this.supportIrrLevel = supportIrrLevel;
    }

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }

    public GuaranteeCompany getGuaranteeCompany() {
        return guaranteeCompany;
    }

    public void setGuaranteeCompany(GuaranteeCompany guaranteeCompany) {
        this.guaranteeCompany = guaranteeCompany;
    }

    public String getRepayStartTime() {
        return repayStartTime;
    }

    public void setRepayStartTime(String repayStartTime) {
        this.repayStartTime = repayStartTime;
    }

    public String getRepayEndTime() {
        return repayEndTime;
    }

    public void setRepayEndTime(String repayEndTime) {
        this.repayEndTime = repayEndTime;
    }

    public AbleStatus getCreditTimeStatus() {
        return creditTimeStatus;
    }

    public void setCreditTimeStatus(AbleStatus creditTimeStatus) {
        this.creditTimeStatus = creditTimeStatus;
    }

    public AbleStatus getLoanTimeStatus() {
        return loanTimeStatus;
    }

    public void setLoanTimeStatus(AbleStatus loanTimeStatus) {
        this.loanTimeStatus = loanTimeStatus;
    }

    public AbleStatus getRepayTimeStatus() {
        return repayTimeStatus;
    }

    public void setRepayTimeStatus(AbleStatus repayTimeStatus) {
        this.repayTimeStatus = repayTimeStatus;
    }

    public WhetherState getRenewedFlag() {
        return renewedFlag;
    }

    public void setRenewedFlag(WhetherState renewedFlag) {
        this.renewedFlag = renewedFlag;
    }

    public ProtocolChannel getProtocolChannel() {
        return protocolChannel;
    }

    public void setProtocolChannel(ProtocolChannel protocolChannel) {
        this.protocolChannel = protocolChannel;
    }

    public String getCapitalNameShort() {
        return capitalNameShort;
    }

    public void setCapitalNameShort(String capitalNameShort) {
        this.capitalNameShort = capitalNameShort;
    }

    public String getCapitalName() {
        return capitalName;
    }

    public void setCapitalName(String capitalName) {
        this.capitalName = capitalName;
    }

    public String getCapitalIntroduction() {
        return capitalIntroduction;
    }

    public void setCapitalIntroduction(String capitalIntroduction) {
        this.capitalIntroduction = capitalIntroduction;
    }

    public String getContactPerson() {
        return contactPerson;
    }

    public void setContactPerson(String contactPerson) {
        this.contactPerson = contactPerson;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getEmailAddress() {
        return emailAddress;
    }

    public void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress;
    }
    @Override
    public String prefix() {
        return "CC";
    }

    @Override
    public void prePersist() {
        if (StringUtil.isBlank(super.getId())) {
            setId(genId());
        }
        super.setCreatedBy("sys");
        super.setCreatedTime(LocalDateTime.now());
    }
}
