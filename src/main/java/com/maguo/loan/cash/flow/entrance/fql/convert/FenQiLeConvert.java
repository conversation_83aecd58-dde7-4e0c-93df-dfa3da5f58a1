package com.maguo.loan.cash.flow.entrance.fql.convert;

import com.alibaba.fastjson2.JSON;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import com.maguo.loan.cash.flow.convert.ConvertMappings;
import com.maguo.loan.cash.flow.dto.OnlineRepayApplyRequest;
import com.maguo.loan.cash.flow.entity.FqlBillDetails;
import com.maguo.loan.cash.flow.entity.FqlCreditApplyRecord;
import com.maguo.loan.cash.flow.entity.FqlLoanApplyRecord;
import com.maguo.loan.cash.flow.entity.FqlRepayApplyRecord;
import com.maguo.loan.cash.flow.entity.FqlSepInInfo;
import com.maguo.loan.cash.flow.entity.FqlSepOutInfo;
import com.maguo.loan.cash.flow.entity.FqlUserBankCard;
import com.maguo.loan.cash.flow.entity.FqlWithholdDetail;
import com.maguo.loan.cash.flow.entity.PreOrder;
import com.maguo.loan.cash.flow.entity.UserInfo;
import com.maguo.loan.cash.flow.entity.UserOcr;
import com.maguo.loan.cash.flow.entrance.fql.dto.credit.FenQiLeCreditApplyRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.credit.UpLoadInfo;
import com.maguo.loan.cash.flow.entrance.fql.dto.loan.LoanApplyRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.FenQiLeRepayRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.FenQiLeRepayTrialResponse;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.request.BillDetailsInfo;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.request.BindCardInfo;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.request.OtherInfo;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.request.SepInInfo;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.request.SepInInfoDetail;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.request.SepOutInfo;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.request.WithholdDetailInfo;
import com.maguo.loan.cash.flow.enums.Education;
import com.maguo.loan.cash.flow.enums.Gender;
import com.maguo.loan.cash.flow.enums.Industry;
import com.maguo.loan.cash.flow.enums.Marriage;
import com.maguo.loan.cash.flow.enums.OrderState;
import com.maguo.loan.cash.flow.enums.Position;
import com.maguo.loan.cash.flow.enums.Relation;
import com.maguo.loan.cash.flow.enums.RepayPurpose;
import com.maguo.loan.cash.flow.vo.TrialResultVo;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;

import static com.maguo.loan.cash.flow.convert.UserConvert.CERT_NO_CITY_LENGTH;
import static com.maguo.loan.cash.flow.convert.UserConvert.CERT_NO_DISTRICT_LENGTH;
import static com.maguo.loan.cash.flow.enums.Relation.CHILDREN;
import static com.maguo.loan.cash.flow.enums.Relation.CLASSMATE;
import static com.maguo.loan.cash.flow.enums.Relation.COLLEAGUE;
import static com.maguo.loan.cash.flow.enums.Relation.FAMILY;
import static com.maguo.loan.cash.flow.enums.Relation.FRIEND;
import static com.maguo.loan.cash.flow.enums.Relation.PARENTS;
import static com.maguo.loan.cash.flow.enums.Relation.RELATIVE;
import static com.maguo.loan.cash.flow.enums.Relation.SIBLING;
import static com.maguo.loan.cash.flow.enums.Relation.SPOUSE;
import static com.maguo.loan.cash.flow.enums.Relation.UNKNOWN;

@Mapper(imports = {LocalDateTime.class, Marriage.class, Industry.class, Relation.class}, unmappedTargetPolicy = ReportingPolicy.IGNORE,
    uses = ConvertMappings.class)
public interface FenQiLeConvert {

    FenQiLeConvert INSTANCE = Mappers.getMapper(FenQiLeConvert.class);
    int CERT_NO_CITY_LENGTH = 4;
    int CERT_NO_DISTRICT_LENGTH = 6;
    int THOUSAND = 1000;



    @Mapping(source = "creditApplyId", target = "orderNo")
//    @Mapping(constant = "SINGLE", target = "amountType")
    @Mapping(source = "mobileNo", target = "openId")
    @Mapping(source = "name", target = "name")
    @Mapping(source = "mobileNo", target = "mobile")
    @Mapping(source = "identiNo", target = "certNo")
    @Mapping(expression = "java(LocalDateTime.now())", target = "applyTime")
    @Mapping(source = "creditAmount", target = "applyAmount")
    @Mapping(source = "loanTerm", target = "applyPeriods")
    @Mapping(constant = "INIT", target = "preOrderState")
    @Mapping(constant = "Y", target = "isAssignBankChannel")
    // bankchannel暂时只有长银
    @Mapping(constant = "CYBK", target = "bankChannel")
    @Mapping(constant = "FQLQY001", target = "flowChannel")
    @Mapping(constant = "FQLQY001", target = "applyChannel")
    // 利率固定24，默认有权益
    @Mapping(constant = "Y", target = "isIncludingEquity")
    @Mapping(constant = "O", target = "equityRecipient")
    PreOrder toPreOrder(@MappingTarget PreOrder preOrder, FenQiLeCreditApplyRequest request);

    @Mapping(source = "uploadInfo", target = "uploadInfo", qualifiedByName = "toUpLoadInfoJson")
    // 利率固定24，默认有权益
    @Mapping(constant = "Y", target = "isIncludingEquity")
    @Mapping(constant = "O", target = "equityRecipient")
    FqlCreditApplyRecord toCreditApplyRecord(FenQiLeCreditApplyRequest request);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(source = "uploadInfo", target = "uploadInfo", qualifiedByName = "toUpLoadInfoJson")
    // 利率固定24，默认有权益
    @Mapping(constant = "Y", target = "isIncludingEquity")
    @Mapping(constant = "O", target = "equityRecipient")
    FqlCreditApplyRecord toCreditApplyRecord(@MappingTarget FqlCreditApplyRecord fqlCreditApplyRecord, FenQiLeCreditApplyRequest request);


    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(source = "preOrder.openId", target = "id")
    @Mapping(source = "preOrder.certNo", target = "certNo")
    @Mapping(source = "preOrder.mobile", target = "mobile")
    @Mapping(source = "preOrder.name", target = "name")
    @Mapping(source = "fqlCreditApplyRecord.maritalStatus", target = "marriage", qualifiedByName="toMarriage")
    @Mapping(source = "fqlCreditApplyRecord.educationLevel", target = "education", qualifiedByName = "toEducation")
    @Mapping(source = "fqlCreditApplyRecord.userOccupation", target = "position", qualifiedByName = "toPosition")
    @Mapping(source = "fqlCreditApplyRecord.livingAddress", target = "livingAddress")
    @Mapping(source = "fqlCreditApplyRecord.identiNo", target = "livingProvinceCode", qualifiedByName = "toProvinceCode")
    @Mapping(source = "fqlCreditApplyRecord.identiNo", target = "livingCityCode", qualifiedByName = "toCityCode")
    @Mapping(source = "fqlCreditApplyRecord.identiNo", target = "livingDistrictCode", qualifiedByName = "toDistrictCode")
    UserInfo toUserInfo(PreOrder preOrder, FqlCreditApplyRecord fqlCreditApplyRecord);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(source = "identiNo", target = "certNo")
    @Mapping(source = "issuedAgency", target = "certSignOrg")
    @Mapping(source = "idAddr", target = "certAddress")
    @Mapping(source = "identiNo", target = "provinceCode", qualifiedByName = "toProvinceCode")
    @Mapping(source = "identiNo", target = "cityCode", qualifiedByName = "toCityCode")
    @Mapping(source = "identiNo", target = "districtCode", qualifiedByName = "toDistrictCode")
    @Mapping(source = "sex", target = "gender", qualifiedByName = "toGender")
    @Mapping(source = "nation", target = "nation")
    UserOcr toUserOcr(FqlCreditApplyRecord fqlCreditApplyRecord);

    // 利率固定24，默认有权益
    @Mapping(constant = "Y", target = "isIncludingEquity")
    @Mapping(constant = "O", target = "equityRecipient")
    FqlLoanApplyRecord toLoanApplyRecord(LoanApplyRequest loanApplyRequest);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    // 利率固定24，默认有权益
    @Mapping(constant = "Y", target = "isIncludingEquity")
    @Mapping(constant = "O", target = "equityRecipient")
    FqlLoanApplyRecord toLoanApplyRecord(@MappingTarget FqlLoanApplyRecord fqlLoanApplyRecord, LoanApplyRequest loanApplyRequest);


    @Mappings({
        @Mapping(source = "amount", target = "repayTotal"),
        @Mapping(source = "principal", target = "repayPrincipal"),
        @Mapping(source = "interest", target = "repayFee"),
        @Mapping(source = "penalty", target = "repayMuclt")
    })
    FenQiLeRepayTrialResponse toRepayTrailResponse(TrialResultVo trialResultVo);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(source = "uid", target = "uid")
    @Mapping(source = "userName", target = "userName")
    @Mapping(source = "cardNo", target = "cardNo")
    @Mapping(source = "bankType", target = "bankType")
    @Mapping(source = "bankBin", target = "bankBin")
    @Mapping(source = "bankFullName", target = "bankFullName")
    @Mapping(source = "idType", target = "idType")
    @Mapping(source = "idNo", target = "idNo")
    @Mapping(source = "phoneNo", target = "phoneNo")
    @Mapping(source = "identityId", target = "identityId")
    @Mapping(source = "standardBankType", target = "standardBankType")
    @Mapping(source = "standardBankCode", target = "standardBankCode")
    FqlUserBankCard toFqlUserBankCard(BindCardInfo bindCardInfo);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(source = "withholdSerialNo", target = "withholdSerialNo")
    @Mapping(source = "partnerCode", target = "partnerCode")
    @Mapping(source = "bankId", target = "bankId")
    @Mapping(source = "withholdAmt", target = "withholdAmt")
    @Mapping(source = "signNum", target = "signNum")
    @Mapping(source = "payMode", target = "payMode")
    @Mapping(source = "subMerchantId", target = "subMerchantId")
    @Mapping(source = "encrpytContent", target = "encrpytContent")
    FqlRepayApplyRecord toFqlRepayApplyRecord(FenQiLeRepayRequest request);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(source = "amt", target = "amt")
    @Mapping(source = "type", target = "type")
    @Mapping(source = "account", target = "account")
    FqlSepOutInfo toFqlSepOutInfo(SepOutInfo sepOutInfo);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(source = "amt", target = "amt")
    @Mapping(source = "sepMerchCode", target = "sepMerchCode")
    @Mapping(source = "sepBankId", target = "sepBankId")
    @Mapping(source = "account", target = "account")
    @Mapping(source = "detail", target = "detail", qualifiedByName = "listToJsonStringSepInInfoDetail")
    FqlSepInInfo toFqlSepInInfo(SepInInfo sepInInfo);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(source = "assetId", target = "assetId")
    @Mapping(source = "capitalLoanNo", target = "capitalLoanNo")
    @Mapping(source = "rpyTotalAmt", target = "rpyTotalAmt")
    @Mapping(source = "rpyType", target = "rpyType")
    @Mapping(source = "rpyDate", target = "rpyDate")
    FqlWithholdDetail toFqlWithholdDetail(WithholdDetailInfo withholdDetailInfo);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(source = "rpyAmt", target = "rpyAmt")
    @Mapping(source = "rpyPrincipal", target = "rpyPrincipal")
    @Mapping(source = "rpyFeeAmt", target = "rpyFeeAmt")
    @Mapping(source = "rpyMuclt", target = "rpyMuclt")
    @Mapping(source = "rpyTerm", target = "rpyTerm")
    FqlBillDetails toFqlBillDetails(BillDetailsInfo billDetailsInfo);

    @Mapping(source = "loanId", target = "loanId")
    @Mapping(source = "period", target = "period")
    @Mapping(source = "repayPurpose", target = "repayPurpose")
    @Mapping(constant = "ONLINE", target = "repayMode")
    @Mapping(constant = "REPAY", target = "repayType")
    @Mapping(constant = "CAPITAL", target = "paySide")
    @Mapping(constant = "USER", target = "operationSource")
    OnlineRepayApplyRequest toOnlineApplyRequest(String loanId, Integer period, RepayPurpose repayPurpose);

    @Named("listToJsonStringSepInInfoDetail")
    default String listToJsonStringSepInInfoDetail(List<SepInInfoDetail> details) {
        if (details == null || details.isEmpty()) {
            return "";
        }
        return JsonUtil.toJsonString(details);
    }

    @Named("listToJsonStringOtherInfo")
    default String listToJsonStringOtherInfo(List<OtherInfo> otherInfo) {
        if (otherInfo == null || otherInfo.isEmpty()) {
            return "";
        }
        return JsonUtil.toJsonString(otherInfo);
    }

    @Named("toUpLoadInfoJson")
    default String toUpLoadInfoJson(List<UpLoadInfo> uploadInfo) {
        if (CollectionUtils.isEmpty(uploadInfo)) {
            return "[{}]";
        }
        return JSON.toJSONString(uploadInfo);
    }

    @Named("toMarriage")
    static Marriage toMarriage(String marriage) {
        return switch (marriage) {
            case "0" -> Marriage.UNMARRIED;
            case "1" -> Marriage.MARRIED;
            default -> Marriage.UNKNOWN;
        };
    }

    @Named("toEducation")
    static Education toEducation(String education) {
        if (StringUtils.isBlank(education)) {
            return Education.UNKNOWN;
        }
        return switch (education) {
            case "1" -> Education.COLLEGE;
            case "2" -> Education.JUNIOR_COLLEGE;
            case "3" -> Education.MASTER;
            case "4" -> Education.DOCTOR;
            case "7" -> Education.JUNIOR_HIGH_SCHOOL;
            case "10" -> Education.JUNIOR_COLLEGE;
            case "11","12" -> Education.HIGH_SCHOOL;
            case "13" -> Education.PRIMARY_SCHOOL;
            default -> Education.UNKNOWN;
        };
    }

    @Named("toPosition")
    default Position toPosition(String occupation) {

        return switch (occupation) {
            case "1"-> Position.NINETEEN;
            case "2"-> Position.TWENTY_ONE;
            case "3"-> Position.TWENTY_THREE;
            case "4"-> Position.EIGHTEEN;
            case "5"-> Position.TWENTY_FOUR;
            case "6"-> Position.THIRTEEN;
            case "7"-> Position.TWENTY_TWO;
            default -> Position.ELEVEN;
        };
    }

    @Named("toProvinceCode")
    default String toProvinceCode(String certNo) {
        if (StringUtil.isBlank(certNo)) {
            return "";
        }
        return certNo.substring(0, 2) + "0000";
    }

    @Named("toCityCode")
    default String toCityCode(String certNo) {
        if (StringUtil.isBlank(certNo)) {
            return "";
        }
        return certNo.substring(0, CERT_NO_CITY_LENGTH) + "00";
    }

    @Named("toDistrictCode")
    default String toDistrictCode(String certNo) {
        if (StringUtil.isBlank(certNo)) {
            return "";
        }
        return certNo.substring(0, CERT_NO_DISTRICT_LENGTH);
    }

    @Named("toGender")
    static Gender toGender(String idSex) {
        if (StringUtils.isBlank(idSex)) {
            return Gender.UNKNOWN;
        }
        return switch (idSex) {
            case "1" -> Gender.MALE;
            case "2" -> Gender.FEMALE;
            default -> Gender.UNKNOWN;
        };
    }

    @Named("toRelationsCode")
    default Relation toFqlRelationsEnum(String relationsCode) {
        return switch (relationsCode) {
            /**父母*/
            case "1" -> PARENTS;
            /**配偶*/
            case "2" -> SPOUSE;
            /**亲戚*/
            case "3" -> RELATIVE;
            /**家人*/
            case "4" -> FAMILY;
            /**子女*/
            case "5" -> CHILDREN;
            /**朋友*/
            case "6" -> FRIEND;
            /**同事*/
            case "7" -> COLLEAGUE;
            /**兄弟姐妹*/
            case "8" -> SIBLING;
            /**同学*/
            case "10" -> CLASSMATE;
            /**未知*/
            default -> UNKNOWN;
        };
    }
}
