package com.maguo.loan.cash.flow.entrance.fql.controller.callBack;

import com.maguo.loan.cash.flow.entrance.fql.controller.FqlApiValidator;
import com.maguo.loan.cash.flow.entrance.fql.service.BaiWeiCallBackService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 百维回调通知接口类
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fenQiLe/callBack/api")
public class BaiWeiCallBackController extends FqlApiValidator {

    private static final Logger logger = LoggerFactory.getLogger(BaiWeiCallBackController.class);

    @Autowired
    private BaiWeiCallBackService baiWeiCallBackService;

}
