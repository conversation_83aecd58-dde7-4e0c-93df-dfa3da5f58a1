package com.maguo.loan.cash.flow.entrance.fql.dto.credit;

import com.maguo.loan.cash.flow.enums.EquityRecipient;
import com.maguo.loan.cash.flow.enums.IsIncludingEquity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;
import java.util.List;

/*
 * @Description: 分期乐授信申请
 * @Author: abai
 * @Date: 2025-8-8 上午 11:38
 */
public class FenQiLeCreditApplyRequest {
    /**
     * 授信申请编号
     */
    @NotBlank(message = "授信申请编号不能为空")
    private String creditApplyId;
    /**
     * 合作方代码
     */
    @NotBlank(message = "合作方代码不能为空")
    private String partnerCode;
    /**
     * 授信金额，单位元
     */
    @NotNull(message = "授信金额不能为空")
    private BigDecimal creditAmount;
    /**
     * 申请人姓名
     */
    @NotBlank(message = "申请人姓名不能为空")
    private String name;
    /**
     * 婚姻状况，0-未婚，1-已婚，3-离异，4-未知，5-丧偶
     */
    @NotBlank(message = "婚姻状况不能为空")
    private String maritalStatus;
    /**
     * 年龄
     */
    @NotNull(message = "年龄不能为空")
    private Integer age;
    /**
     * 性别，0-未知，1-男，2-女
     */
    @NotBlank(message = "性别不能为空")
    private String sex;
    /**
     * 学历， 0未知，1本科，2大专，3硕士，4博士，7初中，10大专(老)，11中专，12高中，13小学
     */
    @NotBlank(message = "学历不能为空")
    private String educationLevel;
    /**
     * 证件类型，1: 身份证（暂时只支持该选项）
     */
    @NotBlank(message = "证件类型不能为空")
    private String identiType;
    /**
     * 证件号码
     */
    @NotBlank(message = "证件号码不能为空")
    private String identiNo;
    /**
     * 身份证有效期起始日期，YYYYMMDD
     */
    @NotBlank(message = "身份证有效期起始日期不能为空")
    private String idCardValidDate;

    /**
     * 身份证有效期结束日期，YYYYMMDD 如果是长期-20991231
     */
    @NotBlank(message = "身份证有效期结束日期不能为空")
    private String idCardExpireDate;
    /**
     * 身份证地址
     */
    @NotBlank(message = "身份证地址不能为空")
    private String idAddr;
    /**
     * 身份证签发机关
     */
    @NotBlank(message = "身份证签发机关不能为空")
    private String issuedAgency;
    /**
     * 出生日期，yyyy-MM-dd
     */
    @NotBlank(message = "出生日期不能为空")
    private String birthday;
    /**
     * 国籍
     */
    @NotBlank(message = "国籍不能为空")
    private String nationality;
    /**
     * 民族
     */
    @NotBlank(message = "民族不能为空")
    private String nation;
    /**
     * 客户手机号
     */
    @NotBlank(message = "客户手机号不能为空")
    private String mobileNo;
    /**
     * 用户银行卡号
     */
    @NotBlank(message = "用户银行卡号不能为空")
    private String userBankCardNo;
    /**
     * 开户行编码
     */
    @NotBlank(message = "开户行编码不能为空")
    private String paymentBankCode;
    /**
     * 期数
     */
    @NotNull(message = "期数不能为空")
    private Integer loanTerm;
    /**
     * 对客展示利率
     */
    @NotNull(message = "对客展示利率不能为空")
    private BigDecimal yearInterestRate;
    /**
     * 单位名称
     */
//    @NotBlank(message = "单位名称不能为空")
    private String companyName;
    /**
     * 第一联系人姓名
     */
    @NotBlank(message = "第一联系人姓名不能为空")
    private String contactName;
    /**
     * 第一联系人手机
     */
    @NotBlank(message = "第一联系人手机不能为空")
    private String contactMobile;
    /**
     * 第一联系人关系
     */
    @NotBlank(message = "第一联系人关系不能为空")
    private String contactRel;
    /**
     * 居住地址
     */
    @NotBlank(message = "居住地址不能为空")
    private String livingAddress;
    /**
     * 客户职业
     */
    @NotBlank(message = "客户职业不能为空")
    private String userOccupation;
    /**
     * 客户行业
     */
    @NotBlank(message = "客户行业不能为空")
    private String userIndustryCategory;
    /**
     * 影像信息
     */
    @Valid
    @NotNull(message = "影像信息不能为空")
    private List<UpLoadInfo> uploadInfo;

    public String getCreditApplyId() {
        return creditApplyId;
    }

    public void setCreditApplyId(String creditApplyId) {
        this.creditApplyId = creditApplyId;
    }

    public String getPartnerCode() {
        return partnerCode;
    }

    public void setPartnerCode(String partnerCode) {
        this.partnerCode = partnerCode;
    }

    public BigDecimal getCreditAmount() {
        return creditAmount;
    }

    public void setCreditAmount(BigDecimal creditAmount) {
        this.creditAmount = creditAmount;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMaritalStatus() {
        return maritalStatus;
    }

    public void setMaritalStatus(String maritalStatus) {
        this.maritalStatus = maritalStatus;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getEducationLevel() {
        return educationLevel;
    }

    public void setEducationLevel(String educationLevel) {
        this.educationLevel = educationLevel;
    }

    public String getIdentiType() {
        return identiType;
    }

    public void setIdentiType(String identiType) {
        this.identiType = identiType;
    }

    public String getIdentiNo() {
        return identiNo;
    }

    public void setIdentiNo(String identiNo) {
        this.identiNo = identiNo;
    }

    public String getIdCardValidDate() {
        return idCardValidDate;
    }

    public void setIdCardValidDate(String idCardValidDate) {
        this.idCardValidDate = idCardValidDate;
    }

    public String getIdCardExpireDate() {
        return idCardExpireDate;
    }

    public void setIdCardExpireDate(String idCardExpireDate) {
        this.idCardExpireDate = idCardExpireDate;
    }

    public String getIdAddr() {
        return idAddr;
    }

    public void setIdAddr(String idAddr) {
        this.idAddr = idAddr;
    }

    public String getIssuedAgency() {
        return issuedAgency;
    }

    public void setIssuedAgency(String issuedAgency) {
        this.issuedAgency = issuedAgency;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getNationality() {
        return nationality;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality;
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }

    public String getMobileNo() {
        return mobileNo;
    }

    public void setMobileNo(String mobileNo) {
        this.mobileNo = mobileNo;
    }

    public String getUserBankCardNo() {
        return userBankCardNo;
    }

    public void setUserBankCardNo(String userBankCardNo) {
        this.userBankCardNo = userBankCardNo;
    }

    public String getPaymentBankCode() {
        return paymentBankCode;
    }

    public void setPaymentBankCode(String paymentBankCode) {
        this.paymentBankCode = paymentBankCode;
    }

    public Integer getLoanTerm() {
        return loanTerm;
    }

    public void setLoanTerm(Integer loanTerm) {
        this.loanTerm = loanTerm;
    }

    public BigDecimal getYearInterestRate() {
        return yearInterestRate;
    }

    public void setYearInterestRate(BigDecimal yearInterestRate) {
        this.yearInterestRate = yearInterestRate;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getContactMobile() {
        return contactMobile;
    }

    public void setContactMobile(String contactMobile) {
        this.contactMobile = contactMobile;
    }

    public String getContactRel() {
        return contactRel;
    }

    public void setContactRel(String contactRel) {
        this.contactRel = contactRel;
    }

    public String getLivingAddress() {
        return livingAddress;
    }

    public void setLivingAddress(String livingAddress) {
        this.livingAddress = livingAddress;
    }

    public String getUserOccupation() {
        return userOccupation;
    }

    public void setUserOccupation(String userOccupation) {
        this.userOccupation = userOccupation;
    }

    public String getUserIndustryCategory() {
        return userIndustryCategory;
    }

    public void setUserIndustryCategory(String userIndustryCategory) {
        this.userIndustryCategory = userIndustryCategory;
    }

    public List<UpLoadInfo> getUploadInfo() {
        return uploadInfo;
    }

    public void setUploadInfo(List<UpLoadInfo> uploadInfo) {
        this.uploadInfo = uploadInfo;
    }
}
