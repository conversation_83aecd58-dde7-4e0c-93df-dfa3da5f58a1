package com.maguo.loan.cash.flow.entrance.fql.service;

import groovy.util.logging.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Description 百维回调接口 业务服务类
 * @Date 2024/8/15 14:40
 * @Version v1.0
 **/
@Slf4j
@Service
public class BaiWeiCallBackService {

    private static final Logger logger = LoggerFactory.getLogger(BaiWeiCallBackService.class);

}
