package com.maguo.loan.cash.flow.entrance.fql.dto.loan;

import java.math.BigDecimal;

public class LoanQueryResponse {

    /**
     * 放款结果,0-成功，1-失败，2-查无此单，99-处理中
     */
    private Integer loanStatus;

    /**
     * 放款结果描述
     */
    private String msg;

    /**
     * 放款时间,YYYY-MM-DD HH:MM:SS
     */
    private String paymentTime;

    /**
     * 放款成功金额
     */
    private BigDecimal loanAmt;

    /**
     * 资金方放款编号
     */
    private String capitalLoanNo;

    public Integer getLoanStatus() {
        return loanStatus;
    }

    public void setLoanStatus(Integer loanStatus) {
        this.loanStatus = loanStatus;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getPaymentTime() {
        return paymentTime;
    }

    public void setPaymentTime(String paymentTime) {
        this.paymentTime = paymentTime;
    }

    public BigDecimal getLoanAmt() {
        return loanAmt;
    }

    public void setLoanAmt(BigDecimal loanAmt) {
        this.loanAmt = loanAmt;
    }

    public String getCapitalLoanNo() {
        return capitalLoanNo;
    }

    public void setCapitalLoanNo(String capitalLoanNo) {
        this.capitalLoanNo = capitalLoanNo;
    }

    public static LoanQueryResponse success(String paymentTime,BigDecimal loanAmt, String capitalLoanNo) {
        LoanQueryResponse result = new LoanQueryResponse();
        result.setLoanStatus(0);
        result.setMsg("请求成功");
        result.setPaymentTime(paymentTime);
        result.setLoanAmt(loanAmt);
        result.setCapitalLoanNo(capitalLoanNo);
        return result;
    }
    public static LoanQueryResponse fail() {
        LoanQueryResponse result = new LoanQueryResponse();
        result.setLoanStatus(1);
        result.setMsg("请求失败");
        return result;
    }
    public static LoanQueryResponse fail(String msg) {
        LoanQueryResponse result = new LoanQueryResponse();
        result.setLoanStatus(1);
        result.setMsg(msg);
        return result;
    }
    public static LoanQueryResponse processing() {
        LoanQueryResponse result = new LoanQueryResponse();
        result.setLoanStatus(99);
        result.setMsg("放款处理中");
        return result;
    }
    public static LoanQueryResponse unknowOrder(String msg) {
        LoanQueryResponse result = new LoanQueryResponse();
        result.setLoanStatus(2);
        result.setMsg(msg);
        return result;
    }
}
