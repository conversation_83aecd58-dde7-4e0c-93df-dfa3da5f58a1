package com.maguo.loan.cash.flow.entrance.fql.controller;

import com.maguo.loan.cash.flow.entrance.fql.dto.credit.CreditQueryRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.credit.CreditQueryResponse;
import com.maguo.loan.cash.flow.entrance.fql.dto.credit.FenQiLeCreditApplyRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.credit.FenQiLeCreditApplyResponse;
import com.maguo.loan.cash.flow.entrance.fql.service.FenQiLeService;
import com.maguo.loan.cash.flow.entrance.ppd.common.ResultCode;
import com.maguo.loan.cash.flow.entrance.ppd.common.ValidationException;
import com.maguo.loan.cash.flow.enums.ApplyChannel;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/*
 * @Description: 分期乐授信接口
 * @Author: abai
 * @Date: 2025-8-8 下午 02:07
 */
@RestController
//@RequestMapping("/abai/api")
@RequestMapping("/fenQiLe/api")
public class FenQiLeCreditController extends FqlApiValidator{
    private static final Logger logger = LoggerFactory.getLogger(FenQiLeCreditController.class);

    @Autowired
    private FenQiLeService fenQiLeService;

    @PostMapping("/creditApply")
    public FenQiLeCreditApplyResponse creditApply(@RequestBody @Valid FenQiLeCreditApplyRequest request, BindingResult bindingResult){
        try {
            // 参数校验
            validate(bindingResult);

            BigDecimal loanAmt = request.getCreditAmount();
            if(loanAmt == null){
                return FenQiLeCreditApplyResponse.fail("放款金额不能为空。");
            }

            boolean isInteger = loanAmt.scale() <= 0 || loanAmt.stripTrailingZeros().scale() <= 0;
            if(!isInteger){
                return FenQiLeCreditApplyResponse.fail("放款金额必须是整数。");
            }

            // 验证范围 [1000, 50000]
            boolean inRange = loanAmt.compareTo(BigDecimal.valueOf(1000)) >= 0 && loanAmt.compareTo(BigDecimal.valueOf(50000)) <= 0;
            if(!inRange){
                return FenQiLeCreditApplyResponse.fail("放款金额必须大于等于1000且小于等于50000。");
            }

            String idExpiryDate = request.getIdCardExpireDate();
            LocalDate expiryDate = LocalDate.parse(idExpiryDate, DateTimeFormatter.ofPattern("yyyyMMdd"));
            if(LocalDate.now().isAfter(expiryDate)) {
                return FenQiLeCreditApplyResponse.fail("身份证已过期。");
            }
            // 验证范围 [1000, 50000]

            boolean isStandardAge =  request.getAge()>= 22 && request.getAge()<= 55;
            if(!isStandardAge){
                return FenQiLeCreditApplyResponse.fail("申请年龄验证不通过,需要在22到55之间。");
            }
//            ApplyChannel applyChannel = ApplyChannel.getApplyChannel(request.getPartnerCode());
//            if (applyChannel == null) {
//                return FenQiLeCreditApplyResponse.fail("未知渠道号");
//            }
            if ("9".equals(request.getUserOccupation())){
                return FenQiLeCreditApplyResponse.fail("申请职业验证不通过");
            }
            if ("145".equals(request.getUserIndustryCategory()) || "146".equals(request.getUserIndustryCategory())){
                return FenQiLeCreditApplyResponse.fail("申请行业验证不通过");
            }

            return fenQiLeService.creditApply(request);
        }catch (Exception e){
            logger.error("分期乐授信失败,请联系管理员", e);
            return FenQiLeCreditApplyResponse.fail("申请失败，系统异常");
        }
    }

    @PostMapping("/creditQuery")
    public CreditQueryResponse creditQuery(@RequestBody @Valid CreditQueryRequest request, BindingResult bindingResult) {
        try {
            // 必填参数校验
            validate(bindingResult);
            //验证
            ApplyChannel applyChannel = ApplyChannel.getApplyChannel(request.getPartnerCode());
            if (applyChannel == null) {
                CreditQueryResponse response = new CreditQueryResponse();
                response.setStatus(1);
                response.setMsg("未知渠道号");
                return response;
            }
            // 业务逻辑
            return fenQiLeService.creditResultQuery(request);
        }catch (Exception e){
            logger.error("分期乐授信申请查询失败", e);
            return CreditQueryResponse.fail("查询失败，系统异常");
        }
    }
}
