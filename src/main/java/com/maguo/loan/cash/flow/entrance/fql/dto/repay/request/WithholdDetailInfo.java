package com.maguo.loan.cash.flow.entrance.fql.dto.repay.request;

import jakarta.validation.constraints.NotBlank;

import java.math.BigDecimal;
import java.util.List;

/**
 * 代扣明细
 */
public class WithholdDetailInfo {
    /**
     * 贷款申请编号
     * 分期乐资产号,每笔借款唯一
     */
    private String assetId;
    /**
     * 资金方放款编号/借据号
     * 资金方订单唯一标识
     */
    @NotBlank(message = "资金方放款编号/借据号不能为空")
    private String capitalLoanNo;
    /**
     * 订单维度的实还总额,保留两位有效数字
     * 单笔订单代扣的总额(单位:分)
     */
    @NotBlank(message = "订单维度的实还总额不能为空")
    private BigDecimal rpyTotalAmt;
    /**
     * 还款类型 0-待还、1-正常还款、2-部分提前还、 3-逾期还款 、4-全部提前还 、5-坏账代偿、 6-回购
     */
    private Integer rpyType;
    /**
     * 代扣日期
     * 用户实还日，用户主动发起是当前日；定时扣款是应还日，格式=yyyy-MM-dd
     */
    private String rpyDate;
    /**
     * (还款账单明细,如果是提前结清,会有多条)(List结构)(包含在withholdDetail中)
     */
    private List<BillDetailsInfo> billDetails;

    public String getAssetId() {
        return assetId;
    }

    public void setAssetId(String assetId) {
        this.assetId = assetId;
    }

    public String getCapitalLoanNo() {
        return capitalLoanNo;
    }

    public void setCapitalLoanNo(String capitalLoanNo) {
        this.capitalLoanNo = capitalLoanNo;
    }

    public BigDecimal getRpyTotalAmt() {
        return rpyTotalAmt;
    }

    public void setRpyTotalAmt(BigDecimal rpyTotalAmt) {
        this.rpyTotalAmt = rpyTotalAmt;
    }

    public Integer getRpyType() {
        return rpyType;
    }

    public void setRpyType(Integer rpyType) {
        this.rpyType = rpyType;
    }

    public String getRpyDate() {
        return rpyDate;
    }

    public void setRpyDate(String rpyDate) {
        this.rpyDate = rpyDate;
    }

    public List<BillDetailsInfo> getBillDetails() {
        return billDetails;
    }

    public void setBillDetails(List<BillDetailsInfo> billDetails) {
        this.billDetails = billDetails;
    }
}
