package com.maguo.loan.cash.flow.entity;



import com.jinghang.common.util.StringUtil;
import com.maguo.loan.cash.flow.enums.AbleStatus;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.ProtocolChannel;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 流量配置
 *
 * <AUTHOR>
 * @TableName flow_config
 */
@Entity
@Table(name = "flow_config")
public class FlowConfig extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 5238223640766125439L;
    /**
     * 流量渠道
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "flow_channel")
    private FlowChannel flowChannel;

    /**
     * 流量授信限额
     */
    @Column(name = "credit_day_amt")
    private BigDecimal creditDayAmt;

    /**
     * 流量放款限额
     */
    @Column(name = "loan_day_amt")
    private BigDecimal loanDayAmt;

    /**
     * 第一绑卡渠道
     */
    @Enumerated(EnumType.STRING)
    private ProtocolChannel firstProtocolChannel;

    /**
     * 第二绑卡渠道
     */
    @Enumerated(EnumType.STRING)
    private ProtocolChannel secondProtocolChannel;

    /**
     * 启用状态
     */
    @Enumerated(EnumType.STRING)
    private AbleStatus enabled;

    /**
     * 资产方简称
     */
    @Column(name = "flow_short_name")
    private String flowShortName;

    /**
     * 资产方主体全称
     */
    @Column(name = "flow_full_name")
    private String flowFullName;

    /**
     * 资方简介
     */
    @Column(name = "flow_desc")
    private String flowDesc;

    /**
     * 联系人
     */
    @Column(name = "contact_person")
    private String contactPerson;

    /**
     * 联系电话
     */
    @Column(name = "contact_phone")
    private String contactPhone;

    /**
     * 邮箱地址
     */
    @Column(name = "email_address")
    private String emailAddress;

    public FlowChannel getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(FlowChannel flowChannel) {
        this.flowChannel = flowChannel;
    }

    public BigDecimal getCreditDayAmt() {
        return creditDayAmt;
    }

    public void setCreditDayAmt(BigDecimal creditDayAmt) {
        this.creditDayAmt = creditDayAmt;
    }

    public BigDecimal getLoanDayAmt() {
        return loanDayAmt;
    }

    public void setLoanDayAmt(BigDecimal loanDayAmt) {
        this.loanDayAmt = loanDayAmt;
    }

    public AbleStatus getEnabled() {
        return enabled;
    }

    public void setEnabled(AbleStatus enabled) {
        this.enabled = enabled;
    }

    public ProtocolChannel getFirstProtocolChannel() {
        return firstProtocolChannel;
    }

    public void setFirstProtocolChannel(ProtocolChannel firstProtocolChannel) {
        this.firstProtocolChannel = firstProtocolChannel;
    }

    public ProtocolChannel getSecondProtocolChannel() {
        return secondProtocolChannel;
    }

    public void setSecondProtocolChannel(ProtocolChannel secondProtocolChannel) {
        this.secondProtocolChannel = secondProtocolChannel;
    }

    public String getFlowShortName() {
        return flowShortName;
    }

    public void setFlowShortName(String flowShortName) {
        this.flowShortName = flowShortName;
    }

    public String getFlowFullName() {
        return flowFullName;
    }

    public void setFlowFullName(String flowFullName) {
        this.flowFullName = flowFullName;
    }

    public String getFlowDesc() {
        return flowDesc;
    }

    public void setFlowDesc(String flowDesc) {
        this.flowDesc = flowDesc;
    }

    public String getContactPerson() {
        return contactPerson;
    }

    public void setContactPerson(String contactPerson) {
        this.contactPerson = contactPerson;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getEmailAddress() {
        return emailAddress;
    }

    public void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress;
    }

    @Override
    protected String prefix() {
        return "FC";
    }

    @Override
    public void prePersist() {
        if (StringUtil.isBlank(super.getId())) {
            setId(genId());
        }
        super.setCreatedBy("sys");
        super.setCreatedTime(LocalDateTime.now());
    }
}
