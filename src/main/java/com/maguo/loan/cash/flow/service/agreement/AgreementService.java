package com.maguo.loan.cash.flow.service.agreement;

import com.alibaba.fastjson2.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jinghang.capital.api.LoanService;
import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.common.util.CurrencyUtil;
import com.jinghang.common.util.DateUtil;
import com.jinghang.common.util.IdGen;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.config.LvXinConfig;
import com.maguo.loan.cash.flow.entity.AgreementShow;
import com.maguo.loan.cash.flow.entity.AgreementSignRelation;
import com.maguo.loan.cash.flow.entity.AgreementSignatureRecord;
import com.maguo.loan.cash.flow.entity.Credit;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.PreOrder;
import com.maguo.loan.cash.flow.entity.RepayPlan;
import com.maguo.loan.cash.flow.entity.UserBankCard;
import com.maguo.loan.cash.flow.entity.UserFace;
import com.maguo.loan.cash.flow.entity.UserFile;
import com.maguo.loan.cash.flow.entity.UserInfo;
import com.maguo.loan.cash.flow.entity.UserOcr;
import com.maguo.loan.cash.flow.entity.UserRiskRecord;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.IsIncludingEquity;
import com.maguo.loan.cash.flow.enums.LoanStage;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.WhetherState;
import com.maguo.loan.cash.flow.remote.core.FinLoanFileService;
import com.maguo.loan.cash.flow.remote.nfsp.req.AgreementSignReq;
import com.maguo.loan.cash.flow.remote.nfsp.rsp.AgreementSignRsp;
import com.maguo.loan.cash.flow.remote.sign.req.AuthDto;
import com.maguo.loan.cash.flow.remote.sign.req.Face;
import com.maguo.loan.cash.flow.remote.sign.req.SignApplyReq;
import com.maguo.loan.cash.flow.remote.sign.res.ResultMsg;
import com.maguo.loan.cash.flow.repository.AgreementSignRelationRepository;
import com.maguo.loan.cash.flow.repository.AgreementSignatureRecordRepository;
import com.maguo.loan.cash.flow.repository.CapitalConfigRepository;
import com.maguo.loan.cash.flow.repository.CreditRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.PreOrderRepository;
import com.maguo.loan.cash.flow.repository.RepayPlanRepository;
import com.maguo.loan.cash.flow.repository.UserBankCardRepository;
import com.maguo.loan.cash.flow.repository.UserFaceRepository;
import com.maguo.loan.cash.flow.repository.UserFileRepository;
import com.maguo.loan.cash.flow.repository.UserInfoRepository;
import com.maguo.loan.cash.flow.repository.UserOcrRepository;
import com.maguo.loan.cash.flow.repository.UserRiskRecordRepository;
import com.maguo.loan.cash.flow.service.FileService;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.WarningService;
import com.maguo.loan.cash.flow.util.AmountUtil;
import com.maguo.loan.cash.flow.util.SftpUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/31
 */
@Service
public class AgreementService {
    private static final Logger logger = LoggerFactory.getLogger(AgreementService.class);

    @Autowired
    private SignatureManager signatureManager;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private UserInfoRepository userInfoRepository;

    @Autowired
    private CreditRepository creditRepository;

    @Autowired
    private LoanRepository loanRepository;

    @Autowired
    private MqService mqService;

    @Autowired
    private AgreementSignatureRecordRepository signatureRecordRepository;

    @Autowired
    private AgreementSignRelationRepository signRelationRepository;

    @Autowired
    private UserRiskRecordRepository userRiskRecordRepository;

    @Autowired
    private UserFileRepository userFileRepository;

    @Autowired
    private FileService fileService;

    @Autowired
    private UserFaceRepository userFaceRepository;

    @Autowired
    private FinLoanFileService finLoanFileService;

    @Autowired
    private WarningService warningService;

    @Autowired
    private UserBankCardRepository userBankCardRepository;

    @Autowired
    private LoanService loanService;

    @Autowired
    private RepayPlanRepository repayPlanRepository;

    @Autowired
    private UserOcrRepository userOcrRepository;

    @Autowired
    private SftpUtils sftpUtils;
    @Autowired
    private LvXinConfig lvXinConfig;


    @Autowired
    private UserRiskRecordRepository riskRecordRepository;


    @Value(value = "${oss.bucket.name}")
    private String ossBucket;

    @Value(value = "${agreement.oss.key.path}")
    private String ossPath;

    @Autowired
    private ResourceLoader resourceLoader;


    @Autowired
    private PreOrderRepository preOrderRepository;

    @Autowired
    private CapitalConfigRepository capitalConfigRepository;

    private static final int REPAY_DAY_LIMIT = 28;

    private static final int CONTRACT_LENGTH = 22;


    /**
     * 阶段
     *
     * @param orderId
     * @param stage
     * @param bankChannel
     */
    public void applySign(String orderId, LoanStage stage, FlowChannel flowChannel,BankChannel bankChannel) {
        logger.info("业务协议签署:{},{}", orderId, stage);

        Order order = orderRepository.findById(orderId).orElseThrow();

        AgreementType.getAgreements(flowChannel,bankChannel, stage).forEach(agreementType -> {
                AgreementSignatureRecord agreementSignature = createSign(order, stage, agreementType);
                if(!ObjectUtils.isEmpty(agreementSignature)) {
                 mqService.submitSignApply(agreementSignature.getId());
                }
            });
    }

    /**
     * 风控前
     *
     * @param riskId 风控id
     */
    public void applyRegisterSign(final String riskId, FlowChannel flowChannel,BankChannel bankChannel) {
        logger.info("注册阶段业务协议签署, riskId: {},flowChannel:{},bankChanel:{}", riskId,flowChannel,bankChannel);
        AgreementType.getRiskAgreements(flowChannel, bankChannel).forEach(agreementType -> {
            // 入库
            AgreementSignatureRecord agreementSignature = createSignRisk(riskId, agreementType);
            // 签章申请
            mqService.submitSignApply(agreementSignature.getId());
        });


    }

    private AgreementSignatureRecord createSignRisk(String riskId, AgreementType agreementType) {
        UserRiskRecord riskRecord = userRiskRecordRepository.findById(riskId).orElseThrow(() -> new BizException(ResultCode.RISK_RECORD_NOT_EXIST));
        String userId = riskRecord.getUserId();
        AgreementSignatureRecord signatureRecord = new AgreementSignatureRecord();
        signatureRecord.setUserId(userId);
        signatureRecord.setRiskId(riskRecord.getId());
        signatureRecord.setFileType(agreementType.getFileType());
        signatureRecord.setLoanStage(agreementType.getLoanStage());
        signatureRecord.setTemplateNo(agreementType.getTemplateNo());
        signatureRecord.setAgreementType(agreementType);
        UserInfo userInfo = userInfoRepository.findById(userId).orElseThrow();
        signatureRecord.setBankMobilePhone(userInfo.getMobile());
        signatureRecord.setPersonName(userInfo.getName());
        signatureRecord.setSignState(ProcessState.INIT);
        signatureRecord.setIdentNo(userInfo.getCertNo());
        signatureRecord.setAddress(userInfo.getLivingAddress());
        signatureRecord = signatureRecordRepository.save(signatureRecord);
        // signatureRelation
        AgreementSignRelation agreementSignRelation = new AgreementSignRelation();
        agreementSignRelation.setRelatedId(riskId);
        agreementSignRelation.setLoanStage(agreementType.getLoanStage());

        agreementSignRelation.setSignApplyId(signatureRecord.getId());
        agreementSignRelation.setUserId(userId);
        agreementSignRelation.setOrderId(null);
        signRelationRepository.save(agreementSignRelation);
        return signatureRecord;
    }


    private AgreementSignatureRecord createSign(Order order, LoanStage stage, AgreementType agreementType) {
        AgreementSignatureRecord signatureRecord = new AgreementSignatureRecord();
        //权益客户需要排除 {咨询服务合同(超捷).pdf}
        if(IsIncludingEquity.Y.equals(order.getIsIncludingEquity())){
            //可以在查询pro-order表
            if("CONSULTING_SERVICE_CONTRACT".equals(agreementType.getFileType().name())){
                return null;
            }
        }
        signatureRecord.setUserId(order.getUserId());
        signatureRecord.setRiskId(order.getRiskId());
        signatureRecord.setFileType(agreementType.getFileType());
        signatureRecord.setLoanStage(agreementType.getLoanStage());
        signatureRecord.setTemplateNo(agreementType.getTemplateNo());
        signatureRecord.setAgreementType(agreementType);
        UserInfo userInfo = userInfoRepository.findById(order.getUserId()).orElseThrow();
        signatureRecord.setBankMobilePhone(userInfo.getMobile());
        signatureRecord.setPersonName(userInfo.getName());
        signatureRecord.setSignState(ProcessState.INIT);
        signatureRecord.setIdentNo(userInfo.getCertNo());
        signatureRecord.setAddress(userInfo.getLivingAddress());
        signatureRecord = signatureRecordRepository.save(signatureRecord);
        // signatureRelation
        AgreementSignRelation agreementSignRelation = new AgreementSignRelation();
        //
        if (LoanStage.CREDIT.equals(stage)) {
            Credit credit = creditRepository.findTopByOrderIdOrderByCreatedTimeDesc(order.getId()).orElseThrow();
            agreementSignRelation.setRelatedId(credit.getId());
        }
        if (LoanStage.LOAN.equals(stage) || LoanStage.REPAY.equals(stage)) {
            Loan loan = loanRepository.findTopByOrderIdOrderByCreatedTimeDesc(order.getId()).orElseThrow(() -> new BizException(ResultCode.LOAN_NOT_EXIST));
            agreementSignRelation.setRelatedId(Optional.ofNullable(loan.getLoanRecordId()).orElse(loan.getId()));
        }

        agreementSignRelation.setLoanStage(stage);
        agreementSignRelation.setSignApplyId(signatureRecord.getId());
        agreementSignRelation.setUserId(order.getUserId());
        agreementSignRelation.setOrderId(order.getId());
        signRelationRepository.save(agreementSignRelation);

        return signatureRecord;
    }


    /**
     * 开始签章，监听方法入口
     *
     * @param signId agreementSignRecord Id
     */
    public void signApply(String signId) {
        logger.info("开始签章{}", JsonUtil.toJsonString(signId));
        AgreementSignatureRecord agreementSignature = signatureRecordRepository.findById(signId).orElseThrow();
        AgreementSignRelation signRelation = signRelationRepository.findBySignApplyId(agreementSignature.getId()).orElseThrow();
        // 请求签署
        //查询签署阶段
        String stage = LoanStage.toLoanStage(signRelation.getLoanStage());
        SignApplyReq signReq = new SignApplyReq();

        buildReq(signRelation, agreementSignature, stage, signReq);
        logger.info("调用签章接口请求参数{}", JsonUtil.toJsonString(signReq));
        ResultMsg stringRestResult = signatureManager.signatureApply(signReq);
        logger.info("调用签章接口返回结果{}", JsonUtil.toJsonString(stringRestResult));
        AgreementSignRsp agreementSignRsp = new AgreementSignRsp();
        agreementSignRsp.setTaskId(stringRestResult.getObject() == null ? null : stringRestResult.getObject().toString());
        agreementSignRsp.setFailMsg(stringRestResult.getMessage());
        agreementSignRsp.setState(ProcessState.PROCESSING);
        agreementSignRsp.setUserId(agreementSignRsp.getUserId());
        agreementSignature.setSignState(ProcessState.PROCESSING);
        agreementSignature.setCommonTaskId(agreementSignRsp.getTaskId());
        agreementSignature = signatureRecordRepository.save(agreementSignature);
        // 查询提交
        mqService.submitSignResultQueryDelay(agreementSignature.getId());
    }

    private AgreementSignReq buildReq(AgreementSignRelation signRelation, AgreementSignatureRecord agreementSignature, String loanStage, SignApplyReq signReq) {
        AgreementSignReq agreementSignReq = new AgreementSignReq();
        agreementSignReq.setTemplateNo(agreementSignature.getTemplateNo());
        agreementSignReq.setPersonName(agreementSignature.getPersonName());
        agreementSignReq.setAddress(agreementSignature.getAddress());
        agreementSignReq.setBankMobilePhone(agreementSignature.getBankMobilePhone());
        agreementSignReq.setMobilePhone(agreementSignature.getBankMobilePhone());
        agreementSignReq.setIdentNo(agreementSignature.getIdentNo());
        agreementSignReq.setIdentTypeCode("0");
        agreementSignReq.setParams(buildParams(agreementSignature, signReq, signRelation));
        return agreementSignReq;
    }


    /**
     * 查询签章入口
     *
     * @param signId 签章记录
     */
    public void querySign(String signId) {
        AgreementSignatureRecord agreementSignature = signatureRecordRepository.findById(signId).orElseThrow();
        logger.info("调用签章查询接口请求参数{}", signId);
        // 查询协议签署结果
        // 查询协议签署结果
        ResultMsg resultMsg = signatureManager.signatureResultQuery(agreementSignature.getCommonTaskId());
        if ("200".equals(resultMsg.getCode())) {
            AgreementType agreementType = agreementSignature.getAgreementType();
            // agreement
            agreementSignature.setSignState(ProcessState.SUCCEED);
            agreementSignature.setCommonOssUrl(resultMsg.getObject() == null ? null : resultMsg.getObject().toString());
            agreementSignature = signatureRecordRepository.save(agreementSignature);
            String userId = agreementSignature.getUserId();
            // loanFile
            UserFile userFile = userFileRepository.findById(agreementSignature.getId()).orElse(new UserFile());
            userFile.setId(agreementSignature.getId());
            userFile.setUserId(userId);
            userFile.setFileType(agreementSignature.getFileType());
            userFile.setFileName(agreementSignature.getFileType().getDesc());

            userFile.setOssBucket(ossBucket);
            //直接使用接口返回的oss-key
            userFile.setOssKey(agreementSignature.getCommonOssUrl());
            userFile.setSignFinal(agreementType.getNeedSignAgain() ? WhetherState.N : WhetherState.Y);
            userFile.setLoanStage(agreementType.getLoanStage());
            logger.info("上传成功后,保存userFile:{}", JSON.toJSONString(userFile));
            String ossFile = fileService.getOssUrl(ossBucket, agreementSignature.getCommonOssUrl());
            logger.info("协议文件：{}", ossFile);
            userFileRepository.save(userFile);
        } else {
            warningService.warn("签章申请,业务失败:" + resultMsg.getMessage());
        }

    }


    private Map<String, String> buildParams(AgreementSignatureRecord agreementSignature,
                                            SignApplyReq signReq, AgreementSignRelation signRelation) {
        UserInfo userInfo = userInfoRepository.findById(signRelation.getUserId()).orElseThrow();
        UserOcr userOcr = userOcrRepository.findByUserId(signRelation.getUserId());
        UserFace userFace = userFaceRepository.findByUserId(signRelation.getUserId());
        agreementSignature.setCommonUserId(signRelation.getUserId());
        LoanStage stage = agreementSignature.getLoanStage();
        signReq.setAcctName(userInfo.getName());
        signReq.setAddress(userInfo.getLivingAddress());
        signReq.setPhone(userInfo.getMobile());

        //只有风控阶段再进行判断是否为分期乐
        if (stage.equals(LoanStage.RISK)){
            // 融担公司判断条件
            String riskId = agreementSignature.getRiskId();//风控id
            PreOrder preOrder = preOrderRepository.findByRiskId(riskId).orElseThrow();
            FlowChannel flowChannel = preOrder.getFlowChannel();//流量渠道
            BankChannel bankChannel = preOrder.getBankChannel();//资金渠道
            if (FlowChannel.FQLQY001.name().equals(flowChannel.name())){
                //获取融担公司
                String guaranteeCompany = capitalConfigRepository.findGuaranteeCompanyByBankChannelAndFlowChannel(bankChannel, flowChannel);
                signReq.setGuaranteeCompany(guaranteeCompany);
            }
        }

        AuthDto authDto = new AuthDto();

        //国徽面
        authDto.setCardNationalEmblem(userOcr.getNationOssKey());
        //人脸
        authDto.setCardPortrait(userOcr.getHeadOssKey());
        authDto.setCardNo(userInfo.getCertNo());
        authDto.setCardType("CRED_PSN_CH_IDCARD");
        authDto.setEviName(IdGen.genId());
        Face face = new Face();
        face.setFaceImage(userFace.getOssKey());
        face.setFaceImageSimilarScore(userFace.getFaceScore() == null ? "0" : userFace.getFaceScore().toString());
        face.setFaceLiveDetectScore(userFace.getFaceScore() == null ? "0" : userFace.getFaceScore().toString());
        face.setInfoVerifySupplier(userFace.getFacialSupplier());
        face.setVerifySerialNumber(userFace.getId());
        long epochMilli = Instant.now().toEpochMilli();
        face.setVerifyTime(String.valueOf(epochMilli));
        authDto.setFace(face);
        authDto.setName(userInfo.getName());
        authDto.setRealNameMethod("FACE");
        authDto.setTimeStamp(epochMilli);
        //同步的用户的id
        signReq.setAuthDto(authDto);
        UserRiskRecord userRiskRecord = userRiskRecordRepository.findTopByUserIdOrderByCreatedTimeDesc(userInfo.getId());
        signReq.setTrafficCode(userRiskRecord.getFlowChannel().name());
        if (!stage.equals(LoanStage.RISK)) {
            Order order = orderRepository.findById(signRelation.getOrderId()).orElseThrow();
            Credit credit = creditRepository.findTopByOrderIdOrderByCreatedTimeDesc(order.getId()).orElseThrow();
            signReq.setCreditId(credit.getId());
            if (StringUtils.isNotBlank(order.getLoanCardId())) {
                UserBankCard userBankCard = userBankCardRepository.findById(order.getLoanCardId()).orElse(null);
                signReq.setBankCardNo(userBankCard.getCardNo());
                signReq.setBankName(userBankCard.getBankName());
            }
            if (stage.equals(LoanStage.LOAN)) {
                Loan loan = loanRepository.findByOrderId(order.getId());
                signReq.setAmount(loan.getAmount().toPlainString());
                signReq.setCapitalRmb(CurrencyUtil.number2Chinese(loan.getAmount()));
                //  signReq.setCreditId(credit.getId());
                // 贷款起始日期
                LocalDate loanStartDate = loan.getLoanTime().toLocalDate();
                LocalDate loanEndDate = loanStartDate.plusMonths(credit.getPeriods().longValue());
                if (loanEndDate.getDayOfMonth() > REPAY_DAY_LIMIT) {
                    loanEndDate = loanEndDate.minusDays(loanEndDate.getDayOfMonth() - REPAY_DAY_LIMIT);
                }
                signReq.setDueDay(String.format("%02d", loanEndDate.getDayOfMonth()));
                signReq.setDueMonth(String.format("%02d", loanEndDate.getMonthValue()));
                signReq.setDueYear(String.valueOf(loanEndDate.getYear()));
                signReq.setLoanActvDay(String.format("%02d", loanStartDate.getDayOfMonth()));
                signReq.setLoanActvMonth(String.format("%02d", loanStartDate.getMonthValue()));
                signReq.setLoanActvYear(String.valueOf(loanStartDate.getYear()));
                signReq.setAmountNo(loan.getLoanContractNo());
                signReq.setStagesNumber(loan.getPeriods().toString());

                List<RepayPlan> byLoanId = repayPlanRepository.findByLoanId(loan.getId());
                BigDecimal serviceFee = AmountUtil.calcSumAmount(byLoanId, item -> AmountUtil.safeAmount(item.getConsultFee()));
                signReq.setServiceFee(serviceFee.toPlainString());
            }
        }
        signReq.setContractCode(agreementSignature.getAgreementType().getTemplateNo());
        String jsonString = JsonUtil.toJsonString(signReq);
        logger.info("buildParams:{}", jsonString);
        ObjectMapper mapper = new ObjectMapper();
        return mapper.convertValue(signReq, Map.class);
    }


    /**
     * 查询已签章协议
     */
    public List<AgreementShow> queryAgreementByRelatedId(String relatedId, LoanStage loanStage) {
        List<UserFile> userFiles = signRelationRepository.getUserFiles(relatedId, loanStage);
        if (CollectionUtils.isEmpty(userFiles)) {
            return new ArrayList<>();
        }
        List<AgreementShow> result = new ArrayList<>(userFiles.size());
        userFiles.stream()
            // 去重
            .collect(Collectors.toMap(UserFile::getFileType, value -> value, (v1, v2)
                -> {
                if (v1.getCreatedTime().isAfter(v2.getCreatedTime())) {
                    return v1;
                }
                return v2;
            })).values().forEach(file -> {
                AgreementShow response = new AgreementShow();
                response.setAgreementName(file.getFileName());
                //获取Url，1小时有效期
                String ossUrl = fileService.getOssUrl(file.getOssBucket(), file.getOssKey(), DateUtil.toDate(LocalDateTime.now().plusHours(1)));
                response.setAgreementUrl(ossUrl);
                result.add(response);
            });
        return result;
    }


}
