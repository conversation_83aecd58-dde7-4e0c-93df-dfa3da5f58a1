package com.maguo.loan.cash.flow.entrance.fql.utils;

import com.maguo.loan.cash.flow.entrance.fql.dto.FqlCommonRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.FqlCommonResponse;
import com.maguo.loan.cash.flow.entrance.ppd.dto.PpdCommonRequest;
import com.maguo.loan.cash.flow.entrance.ppd.dto.PpdCommonResponse;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.Cipher;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.security.Key;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Security;
import java.security.Signature;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 拍拍贷 rsa 工具类.
 * 密钥长度2048
 */
public final class FqlRsaUtil {
    /**
     * RSA 默认算法.
     */
    public static final String ALGORITHM = "RSA";

    /**
     * rsa签名算法sha256.
     */
    public static final String SIGN_ALG_SHA256 = "SHA256WithRSA";

    /**
     * rsa 转换模式
     */
    public static final String TRANSFORMATION = "RSA/ECB/PKCS1Padding";

    /**
     * rsa密钥工厂.
     */
    private static final KeyFactory KEY_FACTORY;

    static {
        Security.addProvider(new BouncyCastleProvider());
        try {
            KEY_FACTORY = KeyFactory.getInstance(ALGORITHM);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }

    private FqlRsaUtil() {

    }


    public static void encryptResponse(FqlCommonResponse response, String ppdPublicKey, String ytxPrivateKey) {

        try {
            String data = response.getBizContent();
            PublicKey key = loadPublicKey(ppdPublicKey);
            byte[] result = encryptByPublicKey(TRANSFORMATION, data.getBytes(StandardCharsets.UTF_8), key);
            String encypted = Base64.getEncoder().encodeToString(result);
            response.setBizContent(encypted);


            sign(response, ytxPrivateKey);
        } catch (GeneralSecurityException | IOException e) {
            throw new RuntimeException("加密失败");
        }

    }

    /**
     * 使用公钥加密.
     *
     * @param transformation 算法填充转换模式
     * @param payload        数据
     * @param publicKey      公钥
     * @return 加密后数据
     * @throws GeneralSecurityException 安全异常
     * @throws IOException              IO异常
     */
    public static byte[] encryptByPublicKey(final String transformation, final byte[] payload, final PublicKey publicKey)
        throws GeneralSecurityException, IOException {
        return encryptArray(transformation, publicKey, payload);
    }

    /**
     * 密钥加密.
     *
     * @param transformation 算法填充转换模式
     * @param key            密钥
     * @param payload        数据
     * @return 加密后数据
     * @throws GeneralSecurityException 安全异常
     * @throws IOException              IO异常
     */
    private static byte[] encryptArray(final String transformation, final Key key, final byte[] payload)
        throws GeneralSecurityException, IOException {
        // 对数据加密
        Cipher cipher = Cipher.getInstance(transformation, BouncyCastleProvider.PROVIDER_NAME);
        cipher.init(Cipher.ENCRYPT_MODE, key);
        return doFinalBySegment(cipher, payload);
    }


    /**
     * 私钥解密.
     *
     * @param transformation 算法填充转换模式
     * @param payload        数据
     * @param privateKey     私钥
     * @return 解密后数据
     * @throws GeneralSecurityException 安全异常
     * @throws IOException              IO异常
     */
    public static byte[] decryptByPrivate(final String transformation, final byte[] payload, final PrivateKey privateKey)
        throws GeneralSecurityException, IOException {
        return decryptArray(transformation, privateKey, payload);
    }

    public static String decryptRequest(String content, String ytxPrivateKey) {
        PrivateKey privateKey = loadPrivateKey(ytxPrivateKey);
        try {
            byte[] result = decryptByPrivate(TRANSFORMATION, Base64.getDecoder().decode(content), privateKey);
            return new String(result, StandardCharsets.UTF_8);
        } catch (GeneralSecurityException | IOException e) {
            throw new RuntimeException("解密失败");
        }

    }


    /**
     * 密钥解密.
     *
     * @param transformation 算法填充转换模式
     * @param key            密钥
     * @param payload        数据
     * @return 解密后数据
     * @throws GeneralSecurityException 安全异常
     * @throws IOException              IO异常
     */
    private static byte[] decryptArray(final String transformation, final Key key, final byte[] payload)
        throws GeneralSecurityException, IOException {
        Cipher cipher = Cipher.getInstance(transformation, BouncyCastleProvider.PROVIDER_NAME);
        cipher.init(Cipher.DECRYPT_MODE, key);
        return doFinalBySegment(cipher, payload);
    }


    /**
     * rsa分段加解密.
     *
     * @param cipher  加密cipher
     * @param payload 数据
     * @return 加解密后的数据
     * @throws GeneralSecurityException 安全异常
     * @throws IOException              IO异常
     */
    private static byte[] doFinalBySegment(final Cipher cipher, final byte[] payload) throws GeneralSecurityException, IOException {
        int blockSize = cipher.getBlockSize();
        int sourceLength = payload.length;
        if (sourceLength <= blockSize) {
            return cipher.doFinal(payload);
        }

        ByteArrayOutputStream os = new ByteArrayOutputStream();
        int offsetIndex = 0;
        int offset = 0;
        while (sourceLength - offset > 0) {
            int size = Math.min(sourceLength - offset, blockSize);
            byte[] buffer = cipher.doFinal(payload, offset, size);
            os.write(buffer);
            offsetIndex++;
            offset = offsetIndex * blockSize;
        }
        return os.toByteArray();
    }


    public static void sign(final FqlCommonResponse response, final String privateKey) {

        String paramStr = null;
        PrivateKey key = loadPrivateKey(privateKey);
        paramStr = response.getBizContent();

        byte[] signed = sign(SIGN_ALG_SHA256, key, paramStr.getBytes(StandardCharsets.UTF_8));
        String s = Base64.getEncoder().encodeToString(signed);
        response.setSign(s);
    }


    /**
     * rsa sign signature.
     *
     * @param signAlgorithm sign algorithm
     * @param privateKey    privateKey
     * @param payload       sign data
     * @return signature
     */
    public static byte[] sign(final String signAlgorithm, final PrivateKey privateKey, final byte[] payload) {
        try {
            Signature signature = Signature.getInstance(signAlgorithm);
            signature.initSign(privateKey);
            signature.update(payload);
            return signature.sign();
        } catch (GeneralSecurityException ex) {
            throw new RuntimeException("rsa sign error", ex);
        }
    }


    public static boolean verify(final FqlCommonRequest request, final String publicKey) {
        String sign = request.getSign();
        PublicKey key = loadPublicKey(publicKey);

        return verify(SIGN_ALG_SHA256, key, request.getBizContent().getBytes(StandardCharsets.UTF_8), Base64.getDecoder().decode(sign));
    }

    /**
     * rsa verify signature.
     *
     * @param signAlgorithm sign algorithm
     * @param publicKey     public key
     * @param payload       sign data
     * @param sign          signature
     * @return verify result.
     */
    public static boolean verify(final String signAlgorithm, final PublicKey publicKey, final byte[] payload, final byte[] sign) {
        try {
            Signature signature = Signature.getInstance(signAlgorithm);
            signature.initVerify(publicKey);
            signature.update(payload);
            return signature.verify(sign);
        } catch (GeneralSecurityException ex) {
            throw new RuntimeException("rsa verify error", ex);
        }
    }


    private static String weaveTransactionContent(Map<String, String> params) {
        List<String> keys = new ArrayList<>(params.keySet());
        keys.remove("sign");
        //验签字段排序
        Collections.sort(keys);
        StringBuilder content = new StringBuilder();
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            String value = params.get(key);
            if (key != null && !"".equals(key) && value != null && !"".equals(value)) {
                content.append(i == 0 ? "" : "&")
                    .append(key)
                    .append("=")
                    .append(value);
            }
        }
        return content.toString();
    }

    /**
     * 从PKCS8私钥字符串中提取私钥.
     *
     * @param privateKeyStr 私钥字符串
     * @return 私钥
     */
    public static PrivateKey loadPrivateKey(final String privateKeyStr) {
        byte[] encoded = Base64.getDecoder().decode(privateKeyStr);
        PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(encoded);

        try {
            return KEY_FACTORY.generatePrivate(pkcs8EncodedKeySpec);
        } catch (InvalidKeySpecException e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 从X509公钥字符串中提取公钥.
     *
     * @param publicKeyStr 公钥字符串
     * @return 公钥
     */
    public static PublicKey loadPublicKey(final String publicKeyStr) {
        byte[] encoded = Base64.getDecoder().decode(publicKeyStr);
        X509EncodedKeySpec x509EncodedKeySpec = new X509EncodedKeySpec(encoded);

        try {
            return KEY_FACTORY.generatePublic(x509EncodedKeySpec);
        } catch (InvalidKeySpecException e) {
            throw new RuntimeException(e);
        }
    }

}
