package com.maguo.loan.cash.flow.entrance.fql.dto.credit;

/*
 * @Description: 分期乐授信申请响应
 * @Author: abai
 * @Date: 2025-8-8 下午 02:04
 */
public class FenQiLeCreditApplyResponse {

    /**
     * 授信审核状态，0-成功，1-失败，2-处理中
     */
    private Integer auditStatus;

    /**
     * 业务处理描述信息
     */
    private String msg;

    public Integer getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }



    public static FenQiLeCreditApplyResponse success() {
        FenQiLeCreditApplyResponse result = new FenQiLeCreditApplyResponse();
        result.setAuditStatus(0);
        result.setMsg("请求成功");
        return result;
    }
    public static FenQiLeCreditApplyResponse fail() {
        FenQiLeCreditApplyResponse result = new FenQiLeCreditApplyResponse();
        result.setAuditStatus(1);
        result.setMsg("请求失败");
        return result;
    }
    public static FenQiLeCreditApplyResponse fail(String msg) {
        FenQiLeCreditApplyResponse result = new FenQiLeCreditApplyResponse();
        result.setAuditStatus(1);
        result.setMsg(msg);
        return result;
    }
    public static FenQiLeCreditApplyResponse processing() {
        FenQiLeCreditApplyResponse result = new FenQiLeCreditApplyResponse();
        result.setAuditStatus(2);
        result.setMsg("授信处理中");
        return result;
    }
}
