package com.maguo.loan.cash.flow.entrance.fql.dto.repay;

import com.maguo.loan.cash.flow.entrance.fql.dto.repay.request.BindCardInfo;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.request.SepInInfo;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.request.SepOutInfo;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.request.WithholdDetailInfo;
import jakarta.validation.constraints.NotBlank;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName RepayRequest
 * <AUTHOR>
 * @Description 还款申请请求参数
 * @Date 2025/8/6 14:37
 * @Version v1.0
 **/
public class FenQiLeRepayRequest {

    /**
     * 代扣请求流水号（分期乐侧支付流水号(为方便分期乐与支付通道对账，通过附加信息传输给支付通道)）
     */
    @NotBlank(message = "代扣请求流水号不能为空")
    private String withholdSerialNo;
    /**
     * 合作方代码（资方定义，给乐信分配的代码）
     */
    @NotBlank(message = "合作方代码不能为空")
    private String partnerCode;
    /**
     * 用户绑卡信息
     */
    @NotBlank(message = "用户绑卡信息不能为空")
    private BindCardInfo bindCardInfo;
    /**
     * 通联需要的银行编码(交易侧透传给接入转换,可以参考中信)
     * 仅通联需要，按通联规范维护的银行编码(交易入参：bindCardInfo.standardBankCode)
     */
    @NotBlank(message = "通联需要的银行编码不能为空")
    private String bankId;
    /**
     * 代扣总金额=用户代扣金额+补差金额（单位：分）
     */
    @NotBlank(message = "代扣总金额不能为空")
    private BigDecimal withholdAmt;
    /**
     * 签约协议号
     * 客户在支付机构的签约协议号
     * (通联以身份证号唯一，宝付以四要素唯一)
     * (交易入参：signNum)
     */
    @NotBlank(message = "签约协议号不能为空")
    private String signNum;
    /**
     * 支付模式
     * 0:银行卡支付
     * 1:余额支付
     * 2:份额支付
     * (交易入参：payMode)
     */
    @NotBlank(message = "支付模式不能为空")
    private Integer payMode;
    /**
     * 分期乐在支付机构的商户号（交易入参:subMerchantId）
     */
    @NotBlank(message = "分期乐在支付机构的商户号不能为空")
    private String subMerchantId;
    /**
     * 出账信息
     */
    @NotBlank(message = "出账信息不能为空")
    private List<SepOutInfo> sepOutInfo;
    /**
     * 分账信息
     */
    @NotBlank(message = "分账信息不能为空")
    private List<SepInInfo> sepInInfo;
    /**
     * 加密报文（要加密的字段见后方文档）
     * 分期乐对出入账信息加密报文，需资方透传给支付机构，与资方申请一致方可扣款成功
     */
    private String encrpytContent;
    /**
     * 代扣明细
     */
    @NotBlank(message = "代扣明细不能为空")
    private List<WithholdDetailInfo> withholdDetail;

    public String getWithholdSerialNo() {
        return withholdSerialNo;
    }

    public void setWithholdSerialNo(String withholdSerialNo) {
        this.withholdSerialNo = withholdSerialNo;
    }

    public String getPartnerCode() {
        return partnerCode;
    }

    public void setPartnerCode(String partnerCode) {
        this.partnerCode = partnerCode;
    }

    public BindCardInfo getBindCardInfo() {
        return bindCardInfo;
    }

    public void setBindCardInfo(BindCardInfo bindCardInfo) {
        this.bindCardInfo = bindCardInfo;
    }

    public String getBankId() {
        return bankId;
    }

    public void setBankId(String bankId) {
        this.bankId = bankId;
    }

    public BigDecimal getWithholdAmt() {
        return withholdAmt;
    }

    public void setWithholdAmt(BigDecimal withholdAmt) {
        this.withholdAmt = withholdAmt;
    }

    public String getSignNum() {
        return signNum;
    }

    public void setSignNum(String signNum) {
        this.signNum = signNum;
    }

    public Integer getPayMode() {
        return payMode;
    }

    public void setPayMode(Integer payMode) {
        this.payMode = payMode;
    }

    public String getSubMerchantId() {
        return subMerchantId;
    }

    public void setSubMerchantId(String subMerchantId) {
        this.subMerchantId = subMerchantId;
    }

    public List<SepOutInfo> getSepOutInfo() {
        return sepOutInfo;
    }

    public void setSepOutInfo(List<SepOutInfo> sepOutInfo) {
        this.sepOutInfo = sepOutInfo;
    }

    public List<SepInInfo> getSepInInfo() {
        return sepInInfo;
    }

    public void setSepInInfo(List<SepInInfo> sepInInfo) {
        this.sepInInfo = sepInInfo;
    }

    public String getEncrpytContent() {
        return encrpytContent;
    }

    public void setEncrpytContent(String encrpytContent) {
        this.encrpytContent = encrpytContent;
    }

    public List<WithholdDetailInfo> getWithholdDetail() {
        return withholdDetail;
    }

    public void setWithholdDetail(List<WithholdDetailInfo> withholdDetail) {
        this.withholdDetail = withholdDetail;
    }
}
