package com.maguo.loan.cash.flow.entrance.fql.config;
import com.maguo.loan.cash.flow.common.RequestUriLogFilter;
import com.maguo.loan.cash.flow.entrance.fql.filter.EncryptFilter;
import jakarta.servlet.Filter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.CompositeFilter;

import java.util.ArrayList;
import java.util.List;


@Configuration
public class FqlFilterConfig {

    @Bean
    public FilterRegistrationBean<CompositeFilter> FqlFilter(FqlConfig fqlConfig) {
        List<Filter> filterList = new ArrayList<>();
        filterList.add(new RequestUriLogFilter());
        filterList.add(new EncryptFilter(fqlConfig));
        CompositeFilter compositeFilter = new CompositeFilter();
        compositeFilter.setFilters(filterList);
        FilterRegistrationBean<CompositeFilter> bean = new FilterRegistrationBean<>(compositeFilter);
        bean.addUrlPatterns("/fenQiLe/*");
        return bean;
    }

}
