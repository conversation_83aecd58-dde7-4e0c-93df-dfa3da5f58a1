/*
 Navicat Premium Dump SQL

 Source Server         : UAT
 Source Server Type    : MySQL
 Source Server Version : 80036 (8.0.36)
 Source Host           : rm-uf6ca2748oc823xt69o.mysql.rds.aliyuncs.com:3306
 Source Schema         : product_reform

 Target Server Type    : MySQL
 Target Server Version : 80036 (8.0.36)
 File Encoding         : 65001

 Date: 18/08/2025 11:33:57
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for project_elements
-- ----------------------------
DROP TABLE IF EXISTS `project_elements`;
CREATE TABLE `project_elements`  (
  `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `project_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '关联的项目唯一编码',
  `drawable_amount_range` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '可提现范围(元) (格式 如 1000-50000)',
  `drawable_amount_step` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单笔提现步长(元)',
  `credit_dark_hours` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '授信黑暗期 (格式 HH:mm-HH:mm)',
  `loan_dark_hours` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用信黑暗期 (格式 HH:mm-HH:mm)',
  `repay_dark_hours` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '还款黑暗期 (格式 HH:mm-HH:mm)',
  `funding_credit_dark_hours` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '资金方授信黑暗期',
  `funding_loan_dark_hours` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '资金方用信黑暗期',
  `funding_repay_dark_hours` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '资金方还款黑暗期',
  `daily_credit_limit` decimal(20, 0) NULL DEFAULT NULL COMMENT '日授信限额(万元)',
  `daily_loan_limit` decimal(20, 0) NULL DEFAULT NULL COMMENT '日放款限额(万元)',
  `credit_lock_days` int NULL DEFAULT NULL COMMENT '授信锁定期限(天)',
  `loan_lock_days` int NULL DEFAULT NULL COMMENT '用信锁定期限(天)',
  `customer_interest_rate` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '对客利率(%)',
  `funding_interest_rate` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '对资利率(%)',
  `age_range` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '年龄范围(岁) (格式 如 22-55)',
  `supported_repay_types` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支持的还款类型 (英文逗号分隔)',
  `loan_terms` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '借款期限 (英文逗号分隔)',
  `capital_route` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '资方路由',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `revision` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '乐观锁',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `project_duration_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目时效类型（LONGTIME, TEMPORARY）',
  `temp_start_time` datetime NOT NULL COMMENT '临时配置有效期起',
  `temp_end_time` datetime NOT NULL COMMENT '临时配置有效期止',
  `enabled` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '启用状态',
  `grace_period_days` int NULL DEFAULT NULL COMMENT '年结顺延宽限期 (天)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '产品要素表-主表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
