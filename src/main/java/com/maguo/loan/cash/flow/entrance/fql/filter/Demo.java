package com.maguo.loan.cash.flow.entrance.fql.filter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.gson.Gson;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.entrance.fql.dto.FqlCommonRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.FqlCommonResponse;
import com.maguo.loan.cash.flow.entrance.fql.exception.FqlBizException;
import com.maguo.loan.cash.flow.entrance.fql.exception.FqlResultCode;
import com.maguo.loan.cash.flow.entrance.fql.filter.sdk.LxClient;
import com.maguo.loan.cash.flow.entrance.fql.filter.sdk.LxRequest;
import com.maguo.loan.cash.flow.entrance.fql.filter.sdk.LxResponse;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class Demo {

    //分期乐公钥(加密解密用)
    private static String lxPublicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCRWW80qT4giJRZroIMMCrcVk8qgS7rwzhLNNuZ7BV6aQ+7UTOIpLTY7zDno8Eht6YLS4YJlLAOAYTu4Ycz+ctFRv8pCipcDybhoLFgTMUJIg+KMg5ysvEp/wQpmEsJxakJd77EFgnfaWCx4TR3F/Nh0/XelSTD2guXEx+rpEF9bQIDAQAB";
    //分期乐私钥(加密解密用)
    private static  String lxPrivateKey = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAJFZbzSpPiCIlFmuggwwKtxWTyqBLuvDOEs025nsFXppD7tRM4iktNjvMOejwSG3pgtLhgmUsA4BhO7hhzP5y0VG/ykKKlwPJuGgsWBMxQkiD4oyDnKy8Sn/BCmYSwnFqQl3vsQWCd9pYLHhNHcX82HT9d6VJMPaC5cTH6ukQX1tAgMBAAECgYB00Amdqv6xKtL/GE6P+Q4ZMoxBPr004RoW5w4uMBw62sC37FScVrK5PdJx95s5u9yIa1P30zWCrBnn2fzM+s4vSXq5ZKhhF9R9+lNsHKJDiAZD0sBIGFEhkBg3g725jGwoC+ll8+tT5OeO1DPrsoVrsIlHe5vNEN5Jh9GJ6xS6gQJBAPcU592X/bApWx0PdhvdFCQcTFLXpYJOhJYqN/yvnvSh2oKI8U+3L3ZpwRgz3POQix0QxSXCfzCUHuhnjafVu90CQQCWmH9fF6c1cfkcJYsezc6VUzeHahM+4VY6E08JIGIbGk/X5QsU3JbFU9+q9guzF96+ffDngjA5sukpgjTOxLbRAkBsB1nxSvkCQrKxnAb528lomG7OcbbNqGLT/RWJh4eqUfHlzNYlEmTQEyD34gUjAPmx9ZkdD1Lo/HofBteqoAqJAkBBRWVnXMQfOP/kgC01M7SHKiNWMNaYmWgbEcVeykdbQeM7Ss2rxBQhAMJfwfa+L+leHQ3t+ZJXX8EJOaMTv1URAkEAk8FOmw1laShgZzS5k/6TTAAYpcoo6YnkDpIYMFXS8KysE3bOS2oCUxvu09HIAI48zerVLUDFH0Vkq+TtIxY/GA==";

    //分期乐公钥(加签解签用)
    private static String lxSignPublicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCRWW80qT4giJRZroIMMCrcVk8qgS7rwzhLNNuZ7BV6aQ+7UTOIpLTY7zDno8Eht6YLS4YJlLAOAYTu4Ycz+ctFRv8pCipcDybhoLFgTMUJIg+KMg5ysvEp/wQpmEsJxakJd77EFgnfaWCx4TR3F/Nh0/XelSTD2guXEx+rpEF9bQIDAQAB";
    //分期乐私钥(加签解签用)
    private static  String lxSignPrivateKey = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAJFZbzSpPiCIlFmuggwwKtxWTyqBLuvDOEs025nsFXppD7tRM4iktNjvMOejwSG3pgtLhgmUsA4BhO7hhzP5y0VG/ykKKlwPJuGgsWBMxQkiD4oyDnKy8Sn/BCmYSwnFqQl3vsQWCd9pYLHhNHcX82HT9d6VJMPaC5cTH6ukQX1tAgMBAAECgYB00Amdqv6xKtL/GE6P+Q4ZMoxBPr004RoW5w4uMBw62sC37FScVrK5PdJx95s5u9yIa1P30zWCrBnn2fzM+s4vSXq5ZKhhF9R9+lNsHKJDiAZD0sBIGFEhkBg3g725jGwoC+ll8+tT5OeO1DPrsoVrsIlHe5vNEN5Jh9GJ6xS6gQJBAPcU592X/bApWx0PdhvdFCQcTFLXpYJOhJYqN/yvnvSh2oKI8U+3L3ZpwRgz3POQix0QxSXCfzCUHuhnjafVu90CQQCWmH9fF6c1cfkcJYsezc6VUzeHahM+4VY6E08JIGIbGk/X5QsU3JbFU9+q9guzF96+ffDngjA5sukpgjTOxLbRAkBsB1nxSvkCQrKxnAb528lomG7OcbbNqGLT/RWJh4eqUfHlzNYlEmTQEyD34gUjAPmx9ZkdD1Lo/HofBteqoAqJAkBBRWVnXMQfOP/kgC01M7SHKiNWMNaYmWgbEcVeykdbQeM7Ss2rxBQhAMJfwfa+L+leHQ3t+ZJXX8EJOaMTv1URAkEAk8FOmw1laShgZzS5k/6TTAAYpcoo6YnkDpIYMFXS8KysE3bOS2oCUxvu09HIAI48zerVLUDFH0Vkq+TtIxY/GA==";



    //合作方公钥(加密解密用)
    private static  String partnerPublicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCHSrUrP4JTmp5kMeQCOsgtlDgvglgyTVmkQ0upwLnEE0tbSKwGPpf4JPYI+qEXt6sp7swvaflaT6PD+8yTPjA9P0KgQWpExDS7BGEyboi0H5IaWGpHV4K+OR2NhPS88Y5pTYoHaATnI9jolComT55ZO2vs4d82kGMMc7m/kixbWQIDAQAB";
    //合作方私钥(加密解密用)
    private static  String partnerPrivateKey = "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAIdKtSs/glOanmQx5AI6yC2UOC+CWDJNWaRDS6nAucQTS1tIrAY+l/gk9gj6oRe3qynuzC9p+VpPo8P7zJM+MD0/QqBBakTENLsEYTJuiLQfkhpYakdXgr45HY2E9LzxjmlNigdoBOcj2OiUKiZPnlk7a+zh3zaQYwxzub+SLFtZAgMBAAECgYAM9tO1eTh72m0jHMTEke1ssK9RniPlbhQQ8YHmmlkyuMaX0LbrQkQ9uwz4f0vAghEcy401XepPBSKkPRS1Z1gn4c3CXVNE+cI9dg5dmdaZADZrBFHtv5pNZmdIbpV79W6tPXArRLkN2Zr9rZPfHOHTvjxNuTX5irtXd9pge1GHoQJBAOcqdOUkxSUDiCt4hZM2ZnO0/nleolhPoBsaGPhSkf3+wu9LzKR1jR9cdUTtwN35awYVTdeD6zU0ASgzFedeK58CQQCV04PwGQ2aVRpgaqVZB9ViL3v2pnbzyJmvmImB9OWyOxPGYRhSjE/Mr638qm6LXnbZC8eUen05EZqnvcMytpYHAkEAsLHFNDmM76Ppe8Dd6Q6V9S+Nq9voH7SNXunt2DHVId6PxjWcMMEho60afht1ZrZ1VesG2wscyEf98mpJ/dX/ZQJASQHztciPf1fe7YKqJdMSYLDgyBTfu/VRJma/AsuSLiJphW6EypzCuewVbRyjl1gls6fwzeJOKMPyR7DJcGnWewJBAJU7gGWYCawAJlEDu2g3nw0Jd4Z11crCVVSVveadsEKmjHbHRNT0AUZ0WOVd2QKl8eT/7lybbw6JE9VqCaHyssY=";

    //合作方公钥(加签解签用)
    private static  String partnerSignPublicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCHSrUrP4JTmp5kMeQCOsgtlDgvglgyTVmkQ0upwLnEE0tbSKwGPpf4JPYI+qEXt6sp7swvaflaT6PD+8yTPjA9P0KgQWpExDS7BGEyboi0H5IaWGpHV4K+OR2NhPS88Y5pTYoHaATnI9jolComT55ZO2vs4d82kGMMc7m/kixbWQIDAQAB";
    //合作方私钥(加签解签用)
    private static  String partnerSignPrivateKey = "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAIdKtSs/glOanmQx5AI6yC2UOC+CWDJNWaRDS6nAucQTS1tIrAY+l/gk9gj6oRe3qynuzC9p+VpPo8P7zJM+MD0/QqBBakTENLsEYTJuiLQfkhpYakdXgr45HY2E9LzxjmlNigdoBOcj2OiUKiZPnlk7a+zh3zaQYwxzub+SLFtZAgMBAAECgYAM9tO1eTh72m0jHMTEke1ssK9RniPlbhQQ8YHmmlkyuMaX0LbrQkQ9uwz4f0vAghEcy401XepPBSKkPRS1Z1gn4c3CXVNE+cI9dg5dmdaZADZrBFHtv5pNZmdIbpV79W6tPXArRLkN2Zr9rZPfHOHTvjxNuTX5irtXd9pge1GHoQJBAOcqdOUkxSUDiCt4hZM2ZnO0/nleolhPoBsaGPhSkf3+wu9LzKR1jR9cdUTtwN35awYVTdeD6zU0ASgzFedeK58CQQCV04PwGQ2aVRpgaqVZB9ViL3v2pnbzyJmvmImB9OWyOxPGYRhSjE/Mr638qm6LXnbZC8eUen05EZqnvcMytpYHAkEAsLHFNDmM76Ppe8Dd6Q6V9S+Nq9voH7SNXunt2DHVId6PxjWcMMEho60afht1ZrZ1VesG2wscyEf98mpJ/dX/ZQJASQHztciPf1fe7YKqJdMSYLDgyBTfu/VRJma/AsuSLiJphW6EypzCuewVbRyjl1gls6fwzeJOKMPyR7DJcGnWewJBAJU7gGWYCawAJlEDu2g3nw0Jd4Z11crCVVSVveadsEKmjHbHRNT0AUZ0WOVd2QKl8eT/7lybbw6JE9VqCaHyssY=";



    public static void main(String[] args) {
        String s2 = bulidLxRequest();
        System.out.println(s2);
        LxClient lxClient = new LxClient();
        FqlCommonResponse response = new FqlCommonResponse();
        response.setCode("001");
        response.setMsg("请求成功");
        response.setTimestamp("2025-08-14 10:26:50");
        response.setBizContent("h8sp3N3ghZWYEDChHHKw7IOuJcDqF4G+bvI6v+c0Vn/q7GQLlPXz8Xrp3RC9OulC7syZxF1MluBmUUI1S1J6f3Jua/RtgBO2FeH95IvI659c4dIL3VZ32I4TNNCQwCZFl9sVrNb2nwfdw0WYd2+kJxAtSuHLn+gRX+Qg/EpBG8aFCU+ESHhJMZ+PxqRzZPxpjMMPFMbm8qJlp69cxPfDBZATUMY1TQarTmadAALp8SeJI4o62y/lSsq0entmsu+Q+fCd2WrzqA3aBuX9IP7aMErvlRuQ4usAxqdJiXnWT27M/mjXhbzEoVQqKL3nZ1NII1qzIoIajc7gI+9+JaIkKQ==");
        Gson gson = new Gson();
        String s1 = gson.toJson(response);
        String s = String.valueOf(s1);
        com.alibaba.fastjson.JSONObject request = JSON.parseObject(s);
        String sign = "hs5LM6OEztCJ45lms9/K3tXxooj4jUR7wd+9YvF7pcAt6OjWmb1wkEitsWHC24Gfs8+43YZZQhp5oOcA4j+EmRNV7AC/P/7OBcNfQxkM4LVa9EIKFUink7klewLxyQQApl27wvE3+xMYhvdOECUcNHvwB4e5d1FA9LVBZJP8gs4=";
        String data = JSON.toJSONString(request, SerializerFeature.MapSortField);
        boolean checkSign = lxClient.verifySign(data, sign, partnerSignPublicKey);
        if (!checkSign) {
            throw new FqlBizException(FqlResultCode.SIGN_VERIFY_FAIL);
        }
        try {
           String decrptString = lxClient.decrypt(request.getString("bizContent"),lxPrivateKey);
            System.out.println(decrptString);
        } catch (Exception e) {
            throw new FqlBizException(FqlResultCode.SIGN_VERIFY_FAIL);
        }
    }
    public static String bulidLxRequest(){
        //分期乐请求合作方
        LxClient lxClient = new LxClient();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("withholdSerialNo","O20200730761992300017");
        jsonObject.put("partnerCode","10001");
        //明文数据转json
        String bizContent = JSON.toJSONString(jsonObject);

        //构建请求数据
        FqlCommonRequest lxRequest = new FqlCommonRequest();
        lxRequest.setPartnerCode("FQLQY001");
        lxRequest.setTimestamp(new SimpleDateFormat("yyyy-MM-dd hh:mm:ss").format(new Date()));
        lxRequest.setBizContent(bizContent);
        //先对请求数据进行加密
        String encrptString = lxClient.encrypt(bizContent,partnerPublicKey);
        System.out.println("乐信侧对报文体内容加密后字符串:" + encrptString);
        lxRequest.setBizContent(encrptString);
        //对整个请求对象进行加签,key排序
        String data = JSON.toJSONString(lxRequest, SerializerFeature.MapSortField);
        String sign = lxClient.sign(data,lxSignPrivateKey);
        System.out.println("乐信侧对发送对象加签之后的字符串:" + sign);
        //对请求数据进行加签
        lxRequest.setSign(sign);
        //构建发送的请求字符串
        String requestStr = JSON.toJSONString(lxRequest, SerializerFeature.MapSortField);
        System.out.println("乐信侧发送请求字符串:" + requestStr);
        return requestStr;
    }
}
