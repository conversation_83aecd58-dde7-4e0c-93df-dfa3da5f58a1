package com.maguo.loan.cash.flow.entrance.fql.dto.repay;

import jakarta.validation.constraints.NotBlank;

/**
 * @ClassName RepayTrialRequest
 * <AUTHOR>
 * @Description 还款试算请求参数
 * @Date 2025/8/6 14:37
 * @Version v1.0
 **/
public class FenQiLeRepayTrialRequest {

    /**
     * 借据号
     */
    @NotBlank(message = "借据号不能为空")
    private String capitalLoanNo;

    /**
     * 结算日(yyyy-MM-dd)
     */
    @NotBlank(message = "结算日不能为空")
    private String repayDate;

    /**
     * 还款期数(提前结清传最小期)
     */
    @NotBlank(message = "还款期数不能为空")
    private Integer repayTerm;

    /**
     * 还款类型 (仅支持10、30)
     * 10：正常还
     * 20：部分提前还
     * 30:全部提前结清
     * 40：逾期还
     * 50：代偿
     */
    @NotBlank(message = "还款类型不能为空")
    private String repayType;

    public String getCapitalLoanNo() {
        return capitalLoanNo;
    }

    public void setCapitalLoanNo(String capitalLoanNo) {
        this.capitalLoanNo = capitalLoanNo;
    }

    public String getRepayDate() {
        return repayDate;
    }

    public void setRepayDate(String repayDate) {
        this.repayDate = repayDate;
    }

    public Integer getRepayTerm() {
        return repayTerm;
    }

    public void setRepayTerm(Integer repayTerm) {
        this.repayTerm = repayTerm;
    }

    public String getRepayType() {
        return repayType;
    }

    public void setRepayType(String repayType) {
        this.repayType = repayType;
    }
}
